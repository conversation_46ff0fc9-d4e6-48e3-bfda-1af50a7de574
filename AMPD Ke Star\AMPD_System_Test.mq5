//+------------------------------------------------------------------+
//|                                           AMPD System Test.mq5   |
//|                        Copyright 2025, AMPD Trading Systems      |
//|                    Test Master Signal Integration System         |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, AMPD Trading Systems"
#property link      "https://AMPD.com"
#property version   "1.0"
#property description "Test Master Signal Integration System"

#property indicator_chart_window
#property indicator_buffers 0
#property indicator_plots   0

//--- Input parameters
input group "=== SYSTEM TEST SETTINGS ==="
input bool TestMasterSignal = true;        // Test Master Signal Controller
input bool TestAMPD_G65 = true;            // Test AMPD G65 V2.0 Optimized
input bool TestEA_Integration = true;      // Test EA Integration
input int TestDurationMinutes = 10;       // Test duration in minutes

//--- Global variables
datetime test_start_time = 0;
int master_signal_handle = INVALID_HANDLE;
int ampd_g65_handle = INVALID_HANDLE;
int test_signals_count = 0;
int test_buy_signals = 0;
int test_sell_signals = 0;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
    test_start_time = TimeCurrent();
    
    Print("🧪 AMPD SYSTEM TEST STARTED");
    Print("═══════════════════════════════════════");
    
    // Test Master Signal Controller
    if(TestMasterSignal) {
        master_signal_handle = iCustom(Symbol(), PERIOD_M1, "AMPD_Master_Signal_Controller");
        if(master_signal_handle == INVALID_HANDLE) {
            Print("❌ FAILED: Master Signal Controller not found");
        } else {
            Print("✅ SUCCESS: Master Signal Controller loaded");
        }
    }
    
    // Test AMPD G65 V2.0 Optimized
    if(TestAMPD_G65) {
        ampd_g65_handle = iCustom(Symbol(), PERIOD_M1, "AMPD_G65_V2_Optimized");
        if(ampd_g65_handle == INVALID_HANDLE) {
            Print("❌ FAILED: AMPD G65 V2.0 Optimized not found");
        } else {
            Print("✅ SUCCESS: AMPD G65 V2.0 Optimized loaded");
        }
    }
    
    // Set timer for testing
    EventSetTimer(5); // Check every 5 seconds
    
    Print("🎯 Test will run for ", TestDurationMinutes, " minutes");
    Print("⏰ Test started at: ", TimeToString(test_start_time, TIME_SECONDS));
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    EventKillTimer();
    
    if(master_signal_handle != INVALID_HANDLE) IndicatorRelease(master_signal_handle);
    if(ampd_g65_handle != INVALID_HANDLE) IndicatorRelease(ampd_g65_handle);
    
    // Print test results
    Print("🧪 AMPD SYSTEM TEST COMPLETED");
    Print("═══════════════════════════════════════");
    Print("📊 Total Signals Detected: ", test_signals_count);
    Print("🟢 Buy Signals: ", test_buy_signals);
    Print("🔴 Sell Signals: ", test_sell_signals);
    Print("⏰ Test Duration: ", (TimeCurrent() - test_start_time) / 60, " minutes");
    Print("═══════════════════════════════════════");
}

//+------------------------------------------------------------------+
//| Timer function for testing                                       |
//+------------------------------------------------------------------+
void OnTimer()
{
    datetime current_time = TimeCurrent();
    
    // Check if test duration exceeded
    if((current_time - test_start_time) >= (TestDurationMinutes * 60)) {
        Print("⏰ Test duration completed");
        ExpertRemove();
        return;
    }
    
    // Test Master Signal Controller
    if(TestMasterSignal && master_signal_handle != INVALID_HANDLE) {
        TestMasterSignalController();
    }
    
    // Test AMPD G65 V2.0 Optimized
    if(TestAMPD_G65 && ampd_g65_handle != INVALID_HANDLE) {
        TestAMPD_G65_Signals();
    }
    
    // Update test status
    UpdateTestStatus();
}

//+------------------------------------------------------------------+
//| Test Master Signal Controller                                    |
//+------------------------------------------------------------------+
void TestMasterSignalController()
{
    double master_buy[], master_sell[];
    
    if(CopyBuffer(master_signal_handle, 0, 0, 2, master_buy) > 0 &&
       CopyBuffer(master_signal_handle, 1, 0, 2, master_sell) > 0) {
        
        // Check for new buy signal
        if(master_buy[0] != EMPTY_VALUE && master_buy[1] == EMPTY_VALUE) {
            test_signals_count++;
            test_buy_signals++;
            Print("🎯 MASTER BUY SIGNAL DETECTED at ", TimeToString(TimeCurrent(), TIME_SECONDS));
            Print("   Signal Price: ", DoubleToString(SymbolInfoDouble(Symbol(), SYMBOL_BID), 5));
        }
        
        // Check for new sell signal
        if(master_sell[0] != EMPTY_VALUE && master_sell[1] == EMPTY_VALUE) {
            test_signals_count++;
            test_sell_signals++;
            Print("🎯 MASTER SELL SIGNAL DETECTED at ", TimeToString(TimeCurrent(), TIME_SECONDS));
            Print("   Signal Price: ", DoubleToString(SymbolInfoDouble(Symbol(), SYMBOL_ASK), 5));
        }
    }
}

//+------------------------------------------------------------------+
//| Test AMPD G65 V2.0 Optimized signals                            |
//+------------------------------------------------------------------+
void TestAMPD_G65_Signals()
{
    double ampd_buy[], ampd_sell[];
    
    if(CopyBuffer(ampd_g65_handle, 0, 0, 2, ampd_buy) > 0 &&
       CopyBuffer(ampd_g65_handle, 1, 0, 2, ampd_sell) > 0) {
        
        // Check for new buy signal
        if(ampd_buy[0] != EMPTY_VALUE && ampd_buy[1] == EMPTY_VALUE) {
            Print("📈 AMPD G65 BUY SIGNAL at ", TimeToString(TimeCurrent(), TIME_SECONDS));
        }
        
        // Check for new sell signal
        if(ampd_sell[0] != EMPTY_VALUE && ampd_sell[1] == EMPTY_VALUE) {
            Print("📉 AMPD G65 SELL SIGNAL at ", TimeToString(TimeCurrent(), TIME_SECONDS));
        }
    }
}

//+------------------------------------------------------------------+
//| Update test status                                               |
//+------------------------------------------------------------------+
void UpdateTestStatus()
{
    static datetime last_status_update = 0;
    datetime current_time = TimeCurrent();
    
    // Update status every 30 seconds
    if(current_time - last_status_update >= 30) {
        double elapsed_minutes = (current_time - test_start_time) / 60.0;
        double remaining_minutes = TestDurationMinutes - elapsed_minutes;
        
        string status = "🧪 AMPD SYSTEM TEST STATUS\n";
        status += "═══════════════════════════\n";
        status += "⏰ Elapsed: " + DoubleToString(elapsed_minutes, 1) + " min\n";
        status += "⏳ Remaining: " + DoubleToString(remaining_minutes, 1) + " min\n";
        status += "📊 Total Signals: " + IntegerToString(test_signals_count) + "\n";
        status += "🟢 Buy Signals: " + IntegerToString(test_buy_signals) + "\n";
        status += "🔴 Sell Signals: " + IntegerToString(test_sell_signals) + "\n";
        status += "🎯 Master Signal: " + ((master_signal_handle != INVALID_HANDLE) ? "✅ ACTIVE" : "❌ ERROR") + "\n";
        status += "📈 AMPD G65: " + ((ampd_g65_handle != INVALID_HANDLE) ? "✅ ACTIVE" : "❌ ERROR") + "\n";
        status += "═══════════════════════════";
        
        Comment(status);
        last_status_update = current_time;
    }
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    return(rates_total);
}
