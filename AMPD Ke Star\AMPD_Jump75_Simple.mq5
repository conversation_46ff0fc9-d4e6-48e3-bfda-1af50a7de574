//+------------------------------------------------------------------+
//|                                    AMPD Jump75 Simple Trading EA |
//|                        Copyright 2025, AMPD Trading Systems      |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, AMPD Trading Systems"
#property link      "https://www.mql5.com"
#property version   "1.00"

//--- Input parameters
input group "=== TRADING SETTINGS ==="
input double LotSize = 0.1;                    // Lot size
input double MaxLotSize = 1.0;                 // Maximum lot size
input int MagicNumber = 123456;                // Magic number
input int Slippage = 3;                        // Slippage in points

input group "=== MARTINGALE SETTINGS ==="
input bool UseMartingale = true;               // Use martingale
input double MartingaleMultiplier = 2.0;       // Martingale multiplier
input int MaxMartingaleLevels = 5;             // Maximum martingale levels
input string LotProgression = "0.1,0.2,0.4,0.8,1.6"; // Lot progression

input group "=== RISK MANAGEMENT ==="
input double DailyProfitTarget = 100.0;       // Daily profit target
input double DailyLossLimit = 50.0;           // Daily loss limit
input double MaxDrawdownPercent = 10.0;       // Maximum drawdown %
input bool EnableTrailingStop = true;         // Enable trailing stop
input double TrailingStopPips = 20.0;         // Trailing stop in pips
input bool EnableBreakeven = true;            // Enable breakeven
input double BreakevenPips = 15.0;            // Breakeven trigger pips
input double BreakevenSecurePips = 5.0;       // Breakeven secure pips

input group "=== TRADING HOURS ==="
input bool UseTradingHours = true;            // Use trading hours
input int StartHour = 8;                      // Start hour
input int EndHour = 18;                       // End hour

input group "=== SIGNAL FILTERS ==="
input bool UseATRFilter = true;               // Use ATR filter
input int ATRPeriod = 14;                     // ATR period
input double ATRMinCoeff = 0.5;               // ATR minimum coefficient
input double ATRMaxCoeff = 2.0;               // ATR maximum coefficient
input bool UseCandleConfirmation = true;      // Use candle confirmation

//--- Global variables
double g_dailyPnL = 0.0;
bool g_dailyTargetHit = false;
bool g_dailyLossHit = false;
int g_buyMartingaleLevel = 0;
int g_sellMartingaleLevel = 0;
double g_lotProgression[];
datetime g_lastBarTime = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("AMPD Jump75 Simple EA initialized");
    
    // Parse lot progression
    ParseLotProgression();
    
    // Reset daily tracking
    ResetDailyTracking();
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("AMPD Jump75 Simple EA deinitialized. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Check if new bar
    if(!IsNewBar()) return;
    
    // Update daily P&L
    UpdateDailyPnL();
    
    // Check daily limits
    if(CheckDailyLimits()) return;
    
    // Check trading hours
    if(!IsWithinTradingHours()) return;
    
    // Manage open trades
    ManageOpenTrades();
    
    // Check for buy signals
    if(ShouldOpenBuy()) {
        OpenBuyTrade();
    }
    
    // Check for sell signals
    if(ShouldOpenSell()) {
        OpenSellTrade();
    }
}

//+------------------------------------------------------------------+
//| Check if new bar                                                 |
//+------------------------------------------------------------------+
bool IsNewBar()
{
    datetime currentBarTime = iTime(Symbol(), PERIOD_CURRENT, 0);
    if(currentBarTime != g_lastBarTime) {
        g_lastBarTime = currentBarTime;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Parse lot progression string                                     |
//+------------------------------------------------------------------+
void ParseLotProgression()
{
    string values[];
    int count = StringSplit(LotProgression, ',', values);
    ArrayResize(g_lotProgression, count);
    
    for(int i = 0; i < count; i++) {
        g_lotProgression[i] = StringToDouble(values[i]);
        if(g_lotProgression[i] <= 0) g_lotProgression[i] = LotSize;
    }
}

//+------------------------------------------------------------------+
//| Reset daily tracking                                             |
//+------------------------------------------------------------------+
void ResetDailyTracking()
{
    static int lastDay = -1;
    int currentDay = TimeDay(TimeCurrent());
    
    if(currentDay != lastDay) {
        g_dailyPnL = 0.0;
        g_dailyTargetHit = false;
        g_dailyLossHit = false;
        g_buyMartingaleLevel = 0;
        g_sellMartingaleLevel = 0;
        lastDay = currentDay;
        Print("Daily tracking reset for new day");
    }
}

//+------------------------------------------------------------------+
//| Update daily P&L                                                 |
//+------------------------------------------------------------------+
void UpdateDailyPnL()
{
    double totalProfit = 0.0;
    datetime startOfDay = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));
    
    // Calculate profit from today's closed trades
    if(HistorySelect(startOfDay, TimeCurrent())) {
        for(int i = 0; i < HistoryDealsTotal(); i++) {
            ulong ticket = HistoryDealGetTicket(i);
            if(HistoryDealGetInteger(ticket, DEAL_MAGIC) == MagicNumber) {
                totalProfit += HistoryDealGetDouble(ticket, DEAL_PROFIT);
            }
        }
    }
    
    g_dailyPnL = totalProfit;
}

//+------------------------------------------------------------------+
//| Check daily limits                                               |
//+------------------------------------------------------------------+
bool CheckDailyLimits()
{
    ResetDailyTracking();
    
    if(DailyProfitTarget > 0 && g_dailyPnL >= DailyProfitTarget && !g_dailyTargetHit) {
        g_dailyTargetHit = true;
        Print("Daily profit target reached: ", g_dailyPnL);
        Comment("Daily Profit Target Hit! P&L: ", DoubleToString(g_dailyPnL, 2));
        return true;
    }
    
    if(DailyLossLimit > 0 && g_dailyPnL <= -DailyLossLimit && !g_dailyLossHit) {
        g_dailyLossHit = true;
        Print("Daily loss limit reached: ", g_dailyPnL);
        Comment("Daily Loss Limit Hit! P&L: ", DoubleToString(g_dailyPnL, 2));
        return true;
    }
    
    return g_dailyTargetHit || g_dailyLossHit;
}

//+------------------------------------------------------------------+
//| Check if within trading hours                                    |
//+------------------------------------------------------------------+
bool IsWithinTradingHours()
{
    if(!UseTradingHours) return true;
    
    int currentHour = TimeHour(TimeCurrent());
    return (currentHour >= StartHour && currentHour < EndHour);
}

//+------------------------------------------------------------------+
//| Calculate lot size with martingale                              |
//+------------------------------------------------------------------+
double CalculateLotSize(bool isBuy)
{
    double lots = LotSize;
    
    if(UseMartingale) {
        int level = isBuy ? g_buyMartingaleLevel : g_sellMartingaleLevel;
        if(level < ArraySize(g_lotProgression)) {
            lots = g_lotProgression[level];
        } else {
            lots = LotSize * MathPow(MartingaleMultiplier, level);
        }
    }
    
    // Validate lot size
    double minLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);
    
    if(lots < minLot) lots = minLot;
    if(lots > maxLot) lots = maxLot;
    if(lots > MaxLotSize) lots = MaxLotSize;
    
    // Normalize to lot step
    lots = MathFloor(lots / lotStep) * lotStep;
    
    return lots;
}

//+------------------------------------------------------------------+
//| Check ATR filter                                                 |
//+------------------------------------------------------------------+
bool PassesATRFilter()
{
    if(!UseATRFilter) return true;
    
    double atr = iATR(Symbol(), PERIOD_CURRENT, ATRPeriod);
    if(atr == 0) return true;
    
    double high = iHigh(Symbol(), PERIOD_CURRENT, 1);
    double low = iLow(Symbol(), PERIOD_CURRENT, 1);
    double candleRange = high - low;
    
    if(candleRange < atr * ATRMinCoeff) return false;
    if(ATRMaxCoeff > 0 && candleRange > atr * ATRMaxCoeff) return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Check bullish engulfing pattern                                  |
//+------------------------------------------------------------------+
bool IsBullishEngulfing()
{
    if(!UseCandleConfirmation) return true;
    
    double open1 = iOpen(Symbol(), PERIOD_CURRENT, 1);
    double close1 = iClose(Symbol(), PERIOD_CURRENT, 1);
    double open2 = iOpen(Symbol(), PERIOD_CURRENT, 2);
    double close2 = iClose(Symbol(), PERIOD_CURRENT, 2);
    
    // Previous candle bearish, current candle bullish and engulfs previous
    return (close2 < open2 && close1 > open1 && open1 < close2 && close1 > open2);
}

//+------------------------------------------------------------------+
//| Check bearish engulfing pattern                                  |
//+------------------------------------------------------------------+
bool IsBearishEngulfing()
{
    if(!UseCandleConfirmation) return true;
    
    double open1 = iOpen(Symbol(), PERIOD_CURRENT, 1);
    double close1 = iClose(Symbol(), PERIOD_CURRENT, 1);
    double open2 = iOpen(Symbol(), PERIOD_CURRENT, 2);
    double close2 = iClose(Symbol(), PERIOD_CURRENT, 2);
    
    // Previous candle bullish, current candle bearish and engulfs previous
    return (close2 > open2 && close1 < open1 && open1 > close2 && close1 < open2);
}

//+------------------------------------------------------------------+
//| Check if should open buy trade                                   |
//+------------------------------------------------------------------+
bool ShouldOpenBuy()
{
    // Check if already have buy positions
    if(HasOpenPosition(POSITION_TYPE_BUY)) return false;

    // Check filters
    if(!PassesATRFilter()) return false;
    if(!IsBullishEngulfing()) return false;

    // Simple signal: price above 20 EMA
    double ema20 = iMA(Symbol(), PERIOD_CURRENT, 20, 0, MODE_EMA, PRICE_CLOSE);
    double currentPrice = iClose(Symbol(), PERIOD_CURRENT, 0);

    return (currentPrice > ema20);
}

//+------------------------------------------------------------------+
//| Check if should open sell trade                                  |
//+------------------------------------------------------------------+
bool ShouldOpenSell()
{
    // Check if already have sell positions
    if(HasOpenPosition(POSITION_TYPE_SELL)) return false;

    // Check filters
    if(!PassesATRFilter()) return false;
    if(!IsBearishEngulfing()) return false;

    // Simple signal: price below 20 EMA
    double ema20 = iMA(Symbol(), PERIOD_CURRENT, 20, 0, MODE_EMA, PRICE_CLOSE);
    double currentPrice = iClose(Symbol(), PERIOD_CURRENT, 0);

    return (currentPrice < ema20);
}

//+------------------------------------------------------------------+
//| Check if has open position of type                               |
//+------------------------------------------------------------------+
bool HasOpenPosition(ENUM_POSITION_TYPE posType)
{
    for(int i = 0; i < PositionsTotal(); i++) {
        if(PositionSelectByIndex(i)) {
            if(PositionGetString(POSITION_SYMBOL) == Symbol() &&
               PositionGetInteger(POSITION_MAGIC) == MagicNumber &&
               PositionGetInteger(POSITION_TYPE) == posType) {
                return true;
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Open buy trade                                                   |
//+------------------------------------------------------------------+
void OpenBuyTrade()
{
    double lots = CalculateLotSize(true);
    double price = SymbolInfoDouble(Symbol(), SYMBOL_ASK);

    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = Symbol();
    request.volume = lots;
    request.type = ORDER_TYPE_BUY;
    request.price = price;
    request.deviation = Slippage;
    request.magic = MagicNumber;
    request.comment = "AMPD_Buy_L" + IntegerToString(g_buyMartingaleLevel);

    if(OrderSend(request, result)) {
        if(result.retcode == TRADE_RETCODE_DONE) {
            Print("Buy order opened: Ticket=", result.order, ", Volume=", lots, ", Price=", result.price);
        } else {
            Print("Buy order failed: Error=", result.retcode, ", Comment=", result.comment);
        }
    }
}

//+------------------------------------------------------------------+
//| Open sell trade                                                  |
//+------------------------------------------------------------------+
void OpenSellTrade()
{
    double lots = CalculateLotSize(false);
    double price = SymbolInfoDouble(Symbol(), SYMBOL_BID);

    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = Symbol();
    request.volume = lots;
    request.type = ORDER_TYPE_SELL;
    request.price = price;
    request.deviation = Slippage;
    request.magic = MagicNumber;
    request.comment = "AMPD_Sell_L" + IntegerToString(g_sellMartingaleLevel);

    if(OrderSend(request, result)) {
        if(result.retcode == TRADE_RETCODE_DONE) {
            Print("Sell order opened: Ticket=", result.order, ", Volume=", lots, ", Price=", result.price);
        } else {
            Print("Sell order failed: Error=", result.retcode, ", Comment=", result.comment);
        }
    }
}

//+------------------------------------------------------------------+
//| Manage open trades                                               |
//+------------------------------------------------------------------+
void ManageOpenTrades()
{
    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        if(!PositionSelectByIndex(i)) continue;
        if(PositionGetString(POSITION_SYMBOL) != Symbol()) continue;
        if(PositionGetInteger(POSITION_MAGIC) != MagicNumber) continue;

        ulong ticket = PositionGetInteger(POSITION_TICKET);
        ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
        double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
        double currentPrice = (posType == POSITION_TYPE_BUY) ?
                              SymbolInfoDouble(Symbol(), SYMBOL_BID) :
                              SymbolInfoDouble(Symbol(), SYMBOL_ASK);
        double sl = PositionGetDouble(POSITION_SL);
        double tp = PositionGetDouble(POSITION_TP);

        double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
        double pipValue = point * 10; // Assuming 5-digit broker

        // Calculate profit in pips
        double profitPips = 0;
        if(posType == POSITION_TYPE_BUY) {
            profitPips = (currentPrice - openPrice) / pipValue;
        } else {
            profitPips = (openPrice - currentPrice) / pipValue;
        }

        // Breakeven logic
        if(EnableBreakeven && profitPips >= BreakevenPips && sl != openPrice) {
            double newSL = 0;
            if(posType == POSITION_TYPE_BUY) {
                newSL = openPrice + (BreakevenSecurePips * pipValue);
            } else {
                newSL = openPrice - (BreakevenSecurePips * pipValue);
            }

            ModifyPosition(ticket, newSL, tp);
        }

        // Trailing stop logic
        if(EnableTrailingStop && profitPips > TrailingStopPips) {
            double newSL = 0;
            bool shouldModify = false;

            if(posType == POSITION_TYPE_BUY) {
                newSL = currentPrice - (TrailingStopPips * pipValue);
                shouldModify = (sl == 0 || newSL > sl);
            } else {
                newSL = currentPrice + (TrailingStopPips * pipValue);
                shouldModify = (sl == 0 || newSL < sl);
            }

            if(shouldModify) {
                ModifyPosition(ticket, newSL, tp);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Modify position                                                  |
//+------------------------------------------------------------------+
void ModifyPosition(ulong ticket, double sl, double tp)
{
    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_SLTP;
    request.position = ticket;
    request.sl = sl;
    request.tp = tp;

    if(OrderSend(request, result)) {
        Print("Position ", ticket, " modified: SL=", sl, ", TP=", tp);
    }
}

//+------------------------------------------------------------------+
//| Trade transaction event                                          |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                        const MqlTradeRequest& request,
                        const MqlTradeResult& result)
{
    if(trans.symbol != Symbol() || trans.magic != MagicNumber) return;

    if(trans.type == TRADE_TRANSACTION_DEAL_ADD) {
        UpdateDailyPnL();

        // Update martingale levels based on deal outcome
        if(UseMartingale && HistoryDealSelect(trans.deal)) {
            double dealProfit = HistoryDealGetDouble(trans.deal, DEAL_PROFIT);
            ENUM_DEAL_ENTRY dealEntry = (ENUM_DEAL_ENTRY)HistoryDealGetInteger(trans.deal, DEAL_ENTRY);

            // Only process exit deals for martingale level updates
            if(dealEntry == DEAL_ENTRY_OUT || dealEntry == DEAL_ENTRY_INOUT) {
                ENUM_DEAL_TYPE dealType = (ENUM_DEAL_TYPE)HistoryDealGetInteger(trans.deal, DEAL_TYPE);

                if(dealProfit > 0) {
                    // Reset martingale level on profit
                    if(dealType == DEAL_TYPE_SELL) { // Closing buy position
                        g_buyMartingaleLevel = 0;
                        Print("Buy martingale level reset to 0 (profit)");
                    } else if(dealType == DEAL_TYPE_BUY) { // Closing sell position
                        g_sellMartingaleLevel = 0;
                        Print("Sell martingale level reset to 0 (profit)");
                    }
                } else {
                    // Increment martingale level on loss
                    if(dealType == DEAL_TYPE_SELL) { // Closing buy position
                        if(g_buyMartingaleLevel < MaxMartingaleLevels) {
                            g_buyMartingaleLevel++;
                            Print("Buy martingale level increased to ", g_buyMartingaleLevel);
                        }
                    } else if(dealType == DEAL_TYPE_BUY) { // Closing sell position
                        if(g_sellMartingaleLevel < MaxMartingaleLevels) {
                            g_sellMartingaleLevel++;
                            Print("Sell martingale level increased to ", g_sellMartingaleLevel);
                        }
                    }
                }
            }
        }

        Print("Trade transaction: Daily P/L updated to: ", DoubleToString(g_dailyPnL, 2));
    }
}
