🎯 AMPD SELL SEPARATE 2 - MAIN SIGNAL SYSTEM GUIDE
═══════════════════════════════════════════════════════════════

📋 SYSTEM OVERVIEW
═══════════════════
✅ AMPD SELL Separate 2 (AMPD 75s M1 chat window) as ONLY main signal provider
✅ Gold arrows = BUY signals | Lime arrows = SELL signals
✅ Trades opened every 1 minute based on signals
✅ Trades closed 30 seconds after opposite signal appears
✅ Real-time signal monitoring and quality results
✅ Same risk management strategy maintained

🎨 SIGNAL COLORS
═══════════════════
🟡 GOLD ARROWS = BUY SIGNALS (clrGold)
🟢 LIME ARROWS = SELL SIGNALS (clrLime)

📁 MODIFIED FILES
═══════════════════
1. AMPD_Jump75_Unified_Optimized.mq5     - Updated EA with AMPD SELL Separate 2 integration
2. AMPD 75s M1 chat window.mq5           - Updated with Gold/Lime colors and optimized parameters
3. This setup guide

🚀 INSTALLATION STEPS
═══════════════════════

STEP 1: Compile Indicators
─────────────────────────
1. Open MetaEditor
2. Compile "AMPD 75s M1 chat window.mq5" (MAIN SIGNAL PROVIDER)
3. Ensure no compilation errors

STEP 2: Compile EA
─────────────────────
1. Compile AMPD_Jump75_Unified_Optimized.mq5
2. Ensure no compilation errors

STEP 3: Setup Chart
─────────────────────
1. Open Jump75 Index chart
2. Set timeframe to M1 (1-minute)
3. Add "AMPD 75s M1 chat window" indicator FIRST
4. Attach AMPD_Jump75_Unified_Optimized EA

⚙️ OPTIMAL EA SETTINGS
═══════════════════════

MAIN SIGNAL PROVIDER:
─────────────────────
✅ UseAMPDSellSeparate2 = true
✅ MainSignalIndicator = "AMPD 75s M1 chat window"
✅ OnlyTradeMainSignals = true
✅ MainSignalValiditySeconds = 60
✅ UseGoldLimeColors = true

1-MINUTE TRADING SETTINGS:
─────────────────────────
✅ EnableOneMinuteTrading = true
✅ TradeOpenIntervalSeconds = 60 (1 minute)
✅ OppositeSignalExitSeconds = 30 (30 seconds)
✅ ForceExitOnOppositeSignal = true
✅ UseRealTimeSignalMonitoring = true

SCALPING OPTIMIZATION:
─────────────────────
✅ ScalpingTimeframe = PERIOD_M1
✅ TargetSignalFrequency = 1 (every 1 minute)
✅ ScalpingPipTarget = 3.0 (optimized)
✅ ScalpingStopLoss = 8.0 (optimized)

INDICATOR SETTINGS:
─────────────────────
✅ RefreshPeriod = 1 (ultra-fast)
✅ Kperiod = 2 (ultra-fast)
✅ Dperiod = 2 (ultra-fast)
✅ Slowing = 2 (ultra-fast)
✅ RSIPeriod = 2 (ultra-fast)
✅ RSISignal = 2 (ultra-fast)

🎯 TRADING BEHAVIOR
═══════════════════

SIGNAL GENERATION:
─────────────────
🟡 GOLD BUY arrows appear on downward price movements
🟢 LIME SELL arrows appear on upward price movements
⚡ Ultra-aggressive parameters for maximum signal frequency
🎯 Signals generated every 1-5 minutes

TRADE EXECUTION:
─────────────────
📊 EA monitors ONLY AMPD SELL Separate 2 signals
⏰ Trades opened every 1 minute when signal appears
🎯 3-pip target, 8-pip stop loss (optimized for scalping)
💰 Same risk management and martingale strategy

EXIT STRATEGY:
─────────────
🟡 When GOLD BUY trade is open → Wait for LIME SELL signal
🟢 When LIME SELL trade is open → Wait for GOLD BUY signal
⏰ 30 seconds after opposite signal → Force close all positions
🔄 Reset and ready for next 1-minute interval

📊 EXPECTED RESULTS
═══════════════════

SIGNAL FREQUENCY:
─────────────────
✅ Gold/Lime arrows on most candles with movement
✅ Trade opportunities every 1-5 minutes
✅ Real-time signal detection and execution
✅ 30-second exit timing for quick scalping

TRADING PERFORMANCE:
─────────────────────
✅ High-frequency scalping opportunities
✅ Quick exits prevent large losses
✅ Optimized pip targets for Jump75 Index
✅ Real-time monitoring ensures accurate timing

🔧 DASHBOARD DISPLAY
═══════════════════

The EA dashboard will show:
─────────────────────────
🎯 MAIN: 🟡 GOLD BUY / 🟢 LIME SELL / 🎯 SCANNING
⏰ TIMING: NEXT IN Xs / EXIT IN Xs / READY
📊 AMPD SELL SEP 2: ✅ ACTIVE / ❌ ERROR
🎯 1-MIN TRADING: ✅ ENABLED / ❌ DISABLED
⚡ 30s EXIT: ✅ ENABLED / ❌ DISABLED
🎨 GOLD/LIME: ✅ ACTIVE / ❌ DISABLED

🧪 TESTING PROCEDURE
═══════════════════════

STEP 1: Verify Signal Generation
─────────────────────────────────
1. Watch for frequent Gold/Lime arrows
2. Check console for signal alerts:
   - "🟡 AMPD GOLD BUY: [Symbol] | Price: [Price]"
   - "🟢 AMPD LIME SELL: [Symbol] | Price: [Price]"
3. Verify arrows appear every 1-5 minutes

STEP 2: Test Trade Execution
─────────────────────────────
1. Enable EA on demo account
2. Watch for trade opening messages:
   - "🟡 GOLD BUY trade opened: Volume=X, SL=X, TP=X"
   - "🟢 LIME SELL trade opened: Volume=X, SL=X, TP=X"
3. Verify 1-minute interval between trades

STEP 3: Test 30-Second Exit
─────────────────────────────
1. Wait for trade to open (Gold or Lime)
2. Watch for opposite signal appearance
3. Check for countdown message:
   - "🟢 LIME SELL signal detected - Starting 30-second exit countdown for BUY trade"
   - "🟡 GOLD BUY signal detected - Starting 30-second exit countdown for SELL trade"
4. Verify position closes after 30 seconds:
   - "⏰ 30-second exit executed - All positions closed"

🔧 TROUBLESHOOTING
═══════════════════

PROBLEM: No Gold/Lime arrows appearing
SOLUTION: Check indicator compilation and parameters

PROBLEM: EA not trading
SOLUTION: Verify UseAMPDSellSeparate2 = true and OnlyTradeMainSignals = true

PROBLEM: No 30-second exit
SOLUTION: Check ForceExitOnOppositeSignal = true and UseRealTimeSignalMonitoring = true

PROBLEM: Wrong colors
SOLUTION: Recompile "AMPD 75s M1 chat window.mq5" with updated colors

PROBLEM: Too frequent/infrequent signals
SOLUTION: Adjust RSI and Stochastic periods (increase for fewer signals, decrease for more)

📈 OPTIMIZATION TIPS
═══════════════════

FOR MORE SIGNALS:
─────────────────
• Reduce RSIPeriod to 1
• Reduce Kperiod/Dperiod to 1
• Set RefreshPeriod to 1

FOR FEWER SIGNALS:
─────────────────
• Increase RSIPeriod to 3-5
• Increase Kperiod/Dperiod to 3-5
• Add volatility filters

FOR FASTER EXITS:
─────────────────
• Reduce OppositeSignalExitSeconds to 15-20
• Enable UseRealTimeSignalMonitoring
• Set RefreshPeriod to 1

FOR BETTER RISK MANAGEMENT:
─────────────────────────
• Adjust ScalpingPipTarget (2-5 pips)
• Adjust ScalpingStopLoss (5-10 pips)
• Enable martingale with conservative progression

🎯 FINAL NOTES
═══════════════

✅ System uses ONLY AMPD SELL Separate 2 as signal provider
✅ Gold arrows trigger BUY trades, Lime arrows trigger SELL trades
✅ 1-minute trade intervals with 30-second exit strategy
✅ Real-time monitoring ensures accurate timing
✅ Same proven risk management strategy maintained
✅ Optimized specifically for Jump75 Index M1 scalping

🚀 The system is now ready for high-frequency scalping with Gold/Lime signal accuracy!

═══════════════════════════════════════════════════════════════
