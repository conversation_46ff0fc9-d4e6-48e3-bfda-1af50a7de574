//+------------------------------------------------------------------+
//|                                           AMPD 75s M1 Signals   |
//|                        Copyright 2025, AMPD Trading Systems      |
//|              Jump75 Index M1 Scalping Signal Generator           |
//+------------------------------------------------------------------+
#property copyright "AMPD Trading Systems"
#property link      "https://ampd.com"
#property description "AMPD 75s M1 - Jump75 scalping signals with visible arrows"
#property version   "1.0"
#property indicator_chart_window
#property indicator_buffers 4
#property indicator_plots   2

// Buy arrows
#property indicator_label1     "AMPD Buy Signal"
#property indicator_type1      DRAW_ARROW
#property indicator_color1     clrLime
#property indicator_width1     3
#property indicator_style1     STYLE_SOLID

// Sell arrows  
#property indicator_label2     "AMPD Sell Signal"
#property indicator_type2      DRAW_ARROW
#property indicator_color2     clrRed
#property indicator_width2     3
#property indicator_style2     STYLE_SOLID

//--- Input parameters for Jump75 SCALPING optimization
input group "=== JUMP75 SCALPING SETTINGS ==="
input int RSI_Period = 5;                    // RSI period (ultra-fast for M1)
input double RSI_Overbought = 70.0;          // RSI overbought level (looser)
input double RSI_Oversold = 30.0;            // RSI oversold level (looser)
input int Stoch_K = 3;                       // Stochastic %K (immediate)
input int Stoch_D = 2;                       // Stochastic %D (minimal)
input int Stoch_Slowing = 1;                 // Stochastic slowing (none)
input int EMA_Period = 8;                    // EMA trend filter (faster)

input group "=== SCALPING SIGNAL SETTINGS ==="
input double Signal_Strength = 0.8;          // Signal sensitivity (more sensitive)
input int ScalpingFrequency = 5;             // Target signals every N minutes
input double MinPipMove = 1.5;               // Minimum pip movement for signal
input double MaxPipMove = 12.0;              // Maximum pip movement for signal
input bool Show_Alerts = true;               // Show popup alerts
input bool Send_Notifications = false;       // Send push notifications
input int Arrow_Gap = 8;                     // Arrow distance from price (closer)

input group "=== REAL-TIME REFRESH ==="
input bool Enable_RealTime = true;           // Enable real-time updates
input int Refresh_Seconds = 1;               // Refresh every N seconds

//--- Indicator buffers
double buy_arrows[];
double sell_arrows[];
double rsi_buffer[];
double signal_strength[];

//--- Indicator handles
int rsi_handle = INVALID_HANDLE;
int stoch_handle = INVALID_HANDLE;
int ema_handle = INVALID_HANDLE;

//--- Global variables
datetime last_refresh = 0;
datetime last_alert_time = 0;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
    // Set indicator buffers
    SetIndexBuffer(0, buy_arrows, INDICATOR_DATA);
    SetIndexBuffer(1, sell_arrows, INDICATOR_DATA);
    SetIndexBuffer(2, rsi_buffer, INDICATOR_CALCULATIONS);
    SetIndexBuffer(3, signal_strength, INDICATOR_CALCULATIONS);
    
    // Set arrow symbols
    PlotIndexSetInteger(0, PLOT_ARROW, 233);  // Up arrow
    PlotIndexSetInteger(1, PLOT_ARROW, 234);  // Down arrow
    
    // Set empty values
    PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, EMPTY_VALUE);
    PlotIndexSetDouble(1, PLOT_EMPTY_VALUE, EMPTY_VALUE);
    
    // Initialize arrays
    ArraySetAsSeries(buy_arrows, true);
    ArraySetAsSeries(sell_arrows, true);
    ArraySetAsSeries(rsi_buffer, true);
    ArraySetAsSeries(signal_strength, true);
    
    // Initialize indicators
    rsi_handle = iRSI(Symbol(), Period(), RSI_Period, PRICE_CLOSE);
    stoch_handle = iStochastic(Symbol(), Period(), Stoch_K, Stoch_D, Stoch_Slowing, MODE_SMA, STO_LOWHIGH);
    ema_handle = iMA(Symbol(), Period(), EMA_Period, 0, MODE_EMA, PRICE_CLOSE);
    
    if(rsi_handle == INVALID_HANDLE || stoch_handle == INVALID_HANDLE || ema_handle == INVALID_HANDLE) {
        Print("AMPD 75s M1: Failed to initialize indicators");
        return INIT_FAILED;
    }
    
    // Set indicator name
    IndicatorSetString(INDICATOR_SHORTNAME, "AMPD 75s M1 Signals");
    
    // Set timer for real-time updates
    if(Enable_RealTime) {
        EventSetTimer(Refresh_Seconds);
    }
    
    Print("AMPD 75s M1 Signals initialized for Jump75 scalping");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Release handles
    if(rsi_handle != INVALID_HANDLE) IndicatorRelease(rsi_handle);
    if(stoch_handle != INVALID_HANDLE) IndicatorRelease(stoch_handle);
    if(ema_handle != INVALID_HANDLE) IndicatorRelease(ema_handle);
    
    // Kill timer
    if(Enable_RealTime) {
        EventKillTimer();
    }
    
    Print("AMPD 75s M1 Signals deinitialized");
}

//+------------------------------------------------------------------+
//| Timer function for real-time updates                             |
//+------------------------------------------------------------------+
void OnTimer()
{
    if(!Enable_RealTime) return;
    
    datetime current_time = TimeCurrent();
    if(current_time - last_refresh >= Refresh_Seconds) {
        ChartRedraw();
        last_refresh = current_time;
    }
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    if(rates_total < RSI_Period + Stoch_K) return 0;
    
    int start = prev_calculated;
    if(start < RSI_Period + Stoch_K) start = RSI_Period + Stoch_K;
    
    // Copy indicator data
    double rsi_data[], stoch_main[], stoch_signal[], ema_data[];
    
    if(CopyBuffer(rsi_handle, 0, 0, rates_total, rsi_data) <= 0) return 0;
    if(CopyBuffer(stoch_handle, 0, 0, rates_total, stoch_main) <= 0) return 0;
    if(CopyBuffer(stoch_handle, 1, 0, rates_total, stoch_signal) <= 0) return 0;
    if(CopyBuffer(ema_handle, 0, 0, rates_total, ema_data) <= 0) return 0;
    
    ArraySetAsSeries(rsi_data, true);
    ArraySetAsSeries(stoch_main, true);
    ArraySetAsSeries(stoch_signal, true);
    ArraySetAsSeries(ema_data, true);
    ArraySetAsSeries(time, true);
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);
    
    // Calculate signals
    for(int i = 0; i < rates_total - start; i++) {
        // Clear previous arrows
        buy_arrows[i] = EMPTY_VALUE;
        sell_arrows[i] = EMPTY_VALUE;
        
        if(i >= rates_total - RSI_Period - Stoch_K) continue;
        
        // Get current values
        double current_rsi = rsi_data[i];
        double current_stoch = stoch_main[i];
        double current_ema = ema_data[i];
        double current_close = close[i];
        
        // Calculate signal strength
        double momentum = 0;
        if(i < rates_total - 1) {
            momentum = (close[i] - close[i+1]) / close[i+1] * 1000;
        }
        
        signal_strength[i] = MathAbs(momentum) * Signal_Strength;
        rsi_buffer[i] = current_rsi;
        
        // SCALPING SIGNAL LOGIC - Every 5 Minutes Target
        bool buy_condition = false;
        bool sell_condition = false;

        // Calculate price movement in pips
        double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
        double pip_value = (point * 10 > 0.001) ? point * 10 : point;
        double price_move_1 = (i > 0) ? (current_close - close[i+1]) / pip_value : 0;
        double price_move_3 = (i > 2) ? (current_close - close[i+3]) / pip_value : 0;
        double candle_range = (high[i] - low[i]) / pip_value;

        // Time-based signal filtering (Every 5 minutes target)
        static datetime last_signal_time = 0;
        datetime current_time = time[i];
        bool time_for_signal = (current_time - last_signal_time >= ScalpingFrequency * 60);

        // Volatility check - must be in good range for scalping
        bool good_volatility = (candle_range >= MinPipMove && candle_range <= MaxPipMove);

        if(good_volatility && time_for_signal && i >= 3) {

            // SCALPING BUY SIGNAL CONDITIONS
            // Looking for dips to buy with oversold indicators
            bool price_dip = (price_move_1 < -1.0) || (price_move_3 < -2.5);
            bool hammer_candle = (current_close > low[i] + (candle_range * 0.6));
            bool oversold_rsi = current_rsi < RSI_Oversold + 5; // Slightly above oversold
            bool oversold_stoch = current_stoch < 35;
            bool momentum_turning = momentum > -1.5; // Not too negative
            bool rsi_rising = (i > 0) ? current_rsi > rsi_data[i+1] : false;

            if((price_dip || hammer_candle) && oversold_rsi && oversold_stoch &&
               momentum_turning && rsi_rising && signal_strength[i] > 0.5) {
                buy_condition = true;
            }

            // SCALPING SELL SIGNAL CONDITIONS
            // Looking for spikes to sell with overbought indicators
            bool price_spike = (price_move_1 > 1.0) || (price_move_3 > 2.5);
            bool shooting_star = (current_close < high[i] - (candle_range * 0.6));
            bool overbought_rsi = current_rsi > RSI_Overbought - 5; // Slightly below overbought
            bool overbought_stoch = current_stoch > 65;
            bool momentum_turning_down = momentum < 1.5; // Not too positive
            bool rsi_falling = (i > 0) ? current_rsi < rsi_data[i+1] : false;

            if((price_spike || shooting_star) && overbought_rsi && overbought_stoch &&
               momentum_turning_down && rsi_falling && signal_strength[i] > 0.5) {
                sell_condition = true;
            }

            // Update last signal time if signal generated
            if(buy_condition || sell_condition) {
                last_signal_time = current_time;
            }
        }
        
        // Additional confluence check
        if(i > 0) {
            bool rsi_turning = false;
            bool stoch_turning = false;
            
            if(buy_condition) {
                rsi_turning = (current_rsi > rsi_data[i+1]);      // RSI starting to rise
                stoch_turning = (current_stoch > stoch_main[i+1]); // Stoch starting to rise
            }
            
            if(sell_condition) {
                rsi_turning = (current_rsi < rsi_data[i+1]);      // RSI starting to fall
                stoch_turning = (current_stoch < stoch_main[i+1]); // Stoch starting to fall
            }
            
            // Place arrows with scalping positioning - NO ADDITIONAL CONFLUENCE NEEDED
            buy_arrows[i] = EMPTY_VALUE;
            sell_arrows[i] = EMPTY_VALUE;

            if(buy_condition) {
                buy_arrows[i] = low[i] - (Arrow_Gap * pip_value);

                // Send scalping alert for new signal
                if(Show_Alerts && i == 0 && time[i] != last_alert_time) {
                    Alert("🟢 AMPD SCALPING BUY: ", Symbol(), " | Price: ", DoubleToString(current_close, 5),
                          " | RSI: ", DoubleToString(current_rsi, 1), " | Range: ", DoubleToString(candle_range, 1), " pips");
                    last_alert_time = time[i];
                }
            }

            if(sell_condition) {
                sell_arrows[i] = high[i] + (Arrow_Gap * pip_value);

                // Send scalping alert for new signal
                if(Show_Alerts && i == 0 && time[i] != last_alert_time) {
                    Alert("🔴 AMPD SCALPING SELL: ", Symbol(), " | Price: ", DoubleToString(current_close, 5),
                          " | RSI: ", DoubleToString(current_rsi, 1), " | Range: ", DoubleToString(candle_range, 1), " pips");
                    last_alert_time = time[i];
                }
            }
        }
    }
    
    return(rates_total);
}

//+------------------------------------------------------------------+
//| Get signal for EA integration                                    |
//+------------------------------------------------------------------+
bool IsBuySignal(int shift = 0)
{
    return (buy_arrows[shift] != EMPTY_VALUE);
}

//+------------------------------------------------------------------+
//| Get sell signal for EA integration                               |
//+------------------------------------------------------------------+
bool IsSellSignal(int shift = 0)
{
    return (sell_arrows[shift] != EMPTY_VALUE);
}

//+------------------------------------------------------------------+
//| Get signal strength                                              |
//+------------------------------------------------------------------+
double GetSignalStrength(int shift = 0)
{
    return signal_strength[shift];
}
