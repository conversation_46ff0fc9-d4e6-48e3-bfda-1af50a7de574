//+------------------------------------------------------------------+
//|                                                     Refresh.mq5  |
//|                        Copyright 2024, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property indicator_chart_window

input int RefreshPeriod = 60;  // Refresh period in seconds

int button_handle;
long chart_id;

datetime last_refresh_time;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
   // Get the current chart ID
   chart_id = ChartID();
   
   // Create the button
   button_handle = CreateButton("RefreshButton", 0, 0, 50, 20, "Refresh");
   
   // Initialize the last refresh time
   last_refresh_time = TimeCurrent();
   
   // Set timer to trigger every second
   EventSetTimer(1);
   
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
  {
   // Delete the button
   ObjectDelete(chart_id, "RefreshButton");
   
   // Kill the timer
   EventKillTimer();
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
   // Nothing to do here for this indicator
   return(rates_total);
  }
//+------------------------------------------------------------------+
//| Timer function                                                   |
//+------------------------------------------------------------------+
void OnTimer()
  {
   datetime current_time = TimeCurrent();
   
   // Check if it's time to refresh
   if(current_time - last_refresh_time >= RefreshPeriod)
     {
      // Refresh the chart
      ChartRedraw(chart_id);
      
      // Update the last refresh time
      last_refresh_time = current_time;
     }
  }
//+------------------------------------------------------------------+
//| Create a button function                                         |
//+------------------------------------------------------------------+
int CreateButton(string name, int x, int y, int width, int height, string text)
  {
   if(ObjectFind(chart_id, name) == -1)
     {
      if(!ObjectCreate(chart_id, name, OBJ_BUTTON, 0, 0, 0))
        {
         Print("Failed to create button");
         return(0);
        }
      ObjectSetInteger(chart_id, name, OBJPROP_XSIZE, width);
      ObjectSetInteger(chart_id, name, OBJPROP_YSIZE, height);
      ObjectSetInteger(chart_id, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
      ObjectSetInteger(chart_id, name, OBJPROP_XDISTANCE, x);
      ObjectSetInteger(chart_id, name, OBJPROP_YDISTANCE, y);
      ObjectSetString(chart_id, name, OBJPROP_TEXT, text);
     }
   return(1);
  }
//+------------------------------------------------------------------+