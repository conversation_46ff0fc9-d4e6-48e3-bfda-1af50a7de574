//+------------------------------------------------------------------+
//|                                    AMPD Auto Refresh Enhanced    |
//|                        Copyright 2025, AMPD Trading Systems      |
//|                     Enhanced Auto Refresh for Real-time Data     |
//+------------------------------------------------------------------+
#property indicator_chart_window

input group "=== AUTO REFRESH SETTINGS ==="
input int RefreshPeriod = 5;                 // Refresh period in seconds
input bool RefreshOnNewTick = true;          // Refresh on every new tick
input bool RefreshOnNewBar = true;           // Refresh on new bar
input bool ShowRefreshButton = true;         // Show manual refresh button
input bool EnableStatusDisplay = true;       // Show refresh status

int button_handle;
long chart_id;
datetime last_refresh_time;
datetime last_bar_time;
int tick_count = 0;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
   // Get the current chart ID
   chart_id = ChartID();

   // Create the refresh button if enabled
   if(ShowRefreshButton) {
       button_handle = CreateButton("AMPDRefreshButton", 10, 30, 80, 25, "AMPD Refresh");
   }

   // Initialize timing variables
   last_refresh_time = TimeCurrent();
   last_bar_time = iTime(Symbol(), Period(), 0);
   tick_count = 0;

   // Set timer for enhanced refresh monitoring
   EventSetTimer(1);

   if(EnableStatusDisplay) {
       Comment("AMPD Auto Refresh: Initialized");
   }

   Print("AMPD Enhanced Auto Refresh initialized - Period: ", RefreshPeriod, "s");
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
  {
   // Delete the button
   if(ShowRefreshButton) {
       ObjectDelete(chart_id, "AMPDRefreshButton");
   }

   // Kill the timer
   EventKillTimer();

   if(EnableStatusDisplay) {
       Comment("");
   }

   Print("AMPD Enhanced Auto Refresh deinitialized. Reason: ", reason);
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
   // Enhanced tick monitoring for real-time refresh
   tick_count++;

   // Check for new bar
   datetime current_bar = time[rates_total-1];
   if(RefreshOnNewBar && current_bar != last_bar_time) {
       last_bar_time = current_bar;
       ForceRefresh("New Bar");
   }

   // Check for new tick refresh
   if(RefreshOnNewTick && tick_count % 5 == 0) { // Every 5th tick
       ForceRefresh("Tick Update");
   }

   return(rates_total);
  }
//+------------------------------------------------------------------+
//| Timer function                                                   |
//+------------------------------------------------------------------+
void OnTimer()
  {
   datetime current_time = TimeCurrent();

   // Check if it's time for periodic refresh
   if(current_time - last_refresh_time >= RefreshPeriod)
     {
      ForceRefresh("Timer");
     }

   // Update status display
   if(EnableStatusDisplay) {
       string status = StringFormat("AMPD Refresh: Last=%s | Ticks=%d | Period=%ds",
                                   TimeToString(last_refresh_time, TIME_SECONDS),
                                   tick_count, RefreshPeriod);
       Comment(status);
   }
  }

//+------------------------------------------------------------------+
//| Force refresh with reason logging                                |
//+------------------------------------------------------------------+
void ForceRefresh(string reason)
  {
   // Refresh all chart elements
   ChartRedraw(chart_id);

   // Force indicator recalculation
   ChartSetInteger(chart_id, CHART_AUTOSCROLL, true);

   // Update refresh time
   last_refresh_time = TimeCurrent();

   // Log refresh action
   if(EnableStatusDisplay) {
       Print("AMPD Refresh triggered by: ", reason, " at ", TimeToString(last_refresh_time, TIME_SECONDS));
   }
  }
//+------------------------------------------------------------------+
//| Create a button function                                         |
//+------------------------------------------------------------------+
int CreateButton(string name, int x, int y, int width, int height, string text)
  {
   if(ObjectFind(chart_id, name) == -1)
     {
      if(!ObjectCreate(chart_id, name, OBJ_BUTTON, 0, 0, 0))
        {
         Print("Failed to create button");
         return(0);
        }
      ObjectSetInteger(chart_id, name, OBJPROP_XSIZE, width);
      ObjectSetInteger(chart_id, name, OBJPROP_YSIZE, height);
      ObjectSetInteger(chart_id, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
      ObjectSetInteger(chart_id, name, OBJPROP_XDISTANCE, x);
      ObjectSetInteger(chart_id, name, OBJPROP_YDISTANCE, y);
      ObjectSetString(chart_id, name, OBJPROP_TEXT, text);
     }
   return(1);
  }
//+------------------------------------------------------------------+