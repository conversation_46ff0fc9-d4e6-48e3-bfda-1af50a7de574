//+------------------------------------------------------------------+
//|                                              AMPD Test Arrows.mq5 |
//|                        Copyright 2025, AMPD Trading Systems      |
//|                   Simple test to verify arrows are displaying     |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, AMPD Trading Systems"
#property link      "https://AMPD.com"
#property version   "1.0"
#property description "Simple test indicator to verify arrows display on every price movement"

#property indicator_chart_window
#property indicator_buffers 2
#property indicator_plots   2

//--- Plot Buy Arrows
#property indicator_label1  "Buy"
#property indicator_type1   DRAW_ARROW
#property indicator_color1  clrLime
#property indicator_style1  STYLE_SOLID
#property indicator_width1  3

//--- Plot Sell Arrows
#property indicator_label2  "Sell"
#property indicator_type2   DRAW_ARROW
#property indicator_color2  clrRed
#property indicator_style2  STYLE_SOLID
#property indicator_width2  3

//--- Input parameters
input group "=== TEST SETTINGS ==="
input bool ShowOnEveryBar = true;           // Show arrows on every bar
input double ArrowDistance = 5.0;           // Arrow distance in pips

//--- Indicator buffers
double buy_arrows[];
double sell_arrows[];

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
    // Set indicator buffers
    SetIndexBuffer(0, buy_arrows, INDICATOR_DATA);
    SetIndexBuffer(1, sell_arrows, INDICATOR_DATA);
    
    // Set arrow symbols
    PlotIndexSetInteger(0, PLOT_ARROW, 233);  // Up arrow for buy
    PlotIndexSetInteger(1, PLOT_ARROW, 234);  // Down arrow for sell
    
    // Set arrow colors
    PlotIndexSetInteger(0, PLOT_LINE_COLOR, clrLime);
    PlotIndexSetInteger(1, PLOT_LINE_COLOR, clrRed);
    
    // Set indicator name
    IndicatorSetString(INDICATOR_SHORTNAME, "AMPD Test Arrows");
    
    Print("AMPD Test Arrows initialized - Should show arrows on every price movement");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    int start = prev_calculated;
    if(start < 1) start = 1;
    
    // Calculate pip value
    double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
    double pip_value = (point * 10 > 0.001) ? point * 10 : point;
    double arrow_distance = ArrowDistance * pip_value;
    
    for(int i = start; i < rates_total; i++) {
        // Initialize arrows
        buy_arrows[i] = EMPTY_VALUE;
        sell_arrows[i] = EMPTY_VALUE;
        
        if(i >= 1) {
            if(ShowOnEveryBar) {
                // Show arrows on every bar for testing
                if(i % 2 == 0) {
                    // Even bars - buy arrow
                    buy_arrows[i] = low[i] - arrow_distance;
                } else {
                    // Odd bars - sell arrow
                    sell_arrows[i] = high[i] + arrow_distance;
                }
            } else {
                // Show arrows only on price movement
                bool price_up = close[i] > close[i-1];
                bool price_down = close[i] < close[i-1];
                
                if(price_down) {
                    buy_arrows[i] = low[i] - arrow_distance;
                }
                
                if(price_up) {
                    sell_arrows[i] = high[i] + arrow_distance;
                }
            }
        }
    }
    
    return(rates_total);
}
