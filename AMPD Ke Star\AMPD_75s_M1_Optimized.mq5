//+------------------------------------------------------------------+
//|                                      AMPD 75s M1 Optimized      |
//|                        Copyright 2025, AMPD Trading Systems      |
//|              Optimized for Jump75 Index M1 Scalping Signals     |
//+------------------------------------------------------------------+
#property copyright "AMPD Trading Systems"
#property link      "https://ampd.com"
#property description "AMPD 75s M1 - Jump75 scalping signal generator"
#property version   "1.1"
#property indicator_chart_window
#property indicator_buffers 8
#property indicator_plots   4

#property indicator_label1     "AMPD 75s Buy"
#property indicator_type1      DRAW_ARROW
#property indicator_color1     clrLime
#property indicator_width1     4

#property indicator_label2     "AMPD 75s Sell"
#property indicator_type2      DRAW_ARROW
#property indicator_color2     clrRed
#property indicator_width2     4

#property indicator_label3     "Signal Line"
#property indicator_type3      DRAW_LINE
#property indicator_color3     clrYellow
#property indicator_width3     2

#property indicator_label4     "Trend Line"
#property indicator_type4      DRAW_LINE
#property indicator_color4     clrCyan
#property indicator_width4     1

//--- Input parameters ULTRA-AGGRESSIVE from template
input group "=== ULTRA-AGGRESSIVE SCALPING SETTINGS ==="
input int RSI_Period = 2;                    // RSI period (IMMEDIATE from template)
input int RSI_Signal_Level = 2;              // RSI signal threshold (from template)
input ENUM_APPLIED_PRICE RSI_Price = PRICE_CLOSE;
input int Stoch_K_Period = 2;                // Stochastic %K period (from template)
input int Stoch_D_Period = 2;                // Stochastic %D period (from template)
input int Stoch_Slowing = 2;                 // Stochastic slowing (from template)
input ENUM_MA_METHOD Stoch_MA_Method = MODE_EMA; // EMA method (from template)
input ENUM_STO_PRICE Stoch_Price_Field = STO_LOWHIGH;

input group "=== ULTRA-AGGRESSIVE OPTIMIZATION ==="
input double Signal_Sensitivity = 1.7;       // Signal sensitivity (from template)
input double Volatility_Filter = 0.1;        // Volatility filter (ultra-loose)
input bool Use_Trend_Filter = false;         // Disable trend filter for immediate response
input int EMA_Trend_Period = 5;              // EMA for trend (ultra-fast)
input int ScalpingFrequency = 1;             // Target signals every 1 minute (ultra-frequent)
input double MinVolatility = 0.1;            // Minimum volatility (ultra-sensitive)
input double MaxVolatility = 50.0;           // Maximum volatility (no limit)

input group "=== AUTO REFRESH ==="
input bool Enable_Auto_Refresh = true;       // Enable auto refresh
input int Refresh_Interval = 3;              // Refresh every N seconds

//--- Indicator buffers
double buy_arrows[];
double sell_arrows[];
double signal_line[];
double trend_line[];
double rsi_buffer[];
double stoch_main[];
double stoch_signal[];
double buy_zone[];
double sell_zone[];

//--- Indicator handles
int rsi_handle = INVALID_HANDLE;
int stoch_handle = INVALID_HANDLE;
int ema_handle = INVALID_HANDLE;

//--- Global variables
datetime last_refresh = 0;
datetime last_bar = 0;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
    // Set indicator buffers
    SetIndexBuffer(0, buy_arrows, INDICATOR_DATA);
    SetIndexBuffer(1, sell_arrows, INDICATOR_DATA);
    SetIndexBuffer(2, signal_line, INDICATOR_DATA);
    SetIndexBuffer(3, trend_line, INDICATOR_DATA);
    SetIndexBuffer(4, rsi_buffer, INDICATOR_CALCULATIONS);
    SetIndexBuffer(5, stoch_main, INDICATOR_CALCULATIONS);
    SetIndexBuffer(6, stoch_signal, INDICATOR_CALCULATIONS);
    SetIndexBuffer(7, buy_zone, INDICATOR_CALCULATIONS);
    SetIndexBuffer(8, sell_zone, INDICATOR_CALCULATIONS);

    // Set arrow symbols
    PlotIndexSetInteger(0, PLOT_ARROW, 233);  // Up arrow for buy
    PlotIndexSetInteger(1, PLOT_ARROW, 234);  // Down arrow for sell

    // Set empty values
    PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, EMPTY_VALUE);
    PlotIndexSetDouble(1, PLOT_EMPTY_VALUE, EMPTY_VALUE);

    // Initialize arrays as series
    ArraySetAsSeries(buy_arrows, true);
    ArraySetAsSeries(sell_arrows, true);
    ArraySetAsSeries(signal_line, true);
    ArraySetAsSeries(trend_line, true);
    
    // Initialize indicators
    rsi_handle = iRSI(Symbol(), Period(), RSI_Period, RSI_Price);
    stoch_handle = iStochastic(Symbol(), Period(), Stoch_K_Period, Stoch_D_Period, 
                              Stoch_Slowing, Stoch_MA_Method, Stoch_Price_Field);
    
    if(Use_Trend_Filter) {
        ema_handle = iMA(Symbol(), Period(), EMA_Trend_Period, 0, MODE_EMA, PRICE_CLOSE);
    }
    
    // Check handles
    if(rsi_handle == INVALID_HANDLE || stoch_handle == INVALID_HANDLE) {
        Print("Failed to initialize indicators");
        return INIT_FAILED;
    }
    
    if(Use_Trend_Filter && ema_handle == INVALID_HANDLE) {
        Print("Failed to initialize EMA indicator");
        return INIT_FAILED;
    }
    
    // Set indicator properties
    IndicatorSetString(INDICATOR_SHORTNAME, "AMPD 75s M1 Optimized");
    IndicatorSetInteger(INDICATOR_DIGITS, 2);
    
    // Set levels
    IndicatorSetDouble(INDICATOR_LEVELVALUE, 0, 80);  // Overbought
    IndicatorSetDouble(INDICATOR_LEVELVALUE, 1, 20);  // Oversold
    IndicatorSetDouble(INDICATOR_LEVELVALUE, 2, 50);  // Neutral
    
    // Initialize timer for auto refresh
    if(Enable_Auto_Refresh) {
        EventSetTimer(1);
    }
    
    Print("AMPD 75s M1 Optimized initialized for Jump75 scalping");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Release indicator handles
    if(rsi_handle != INVALID_HANDLE) IndicatorRelease(rsi_handle);
    if(stoch_handle != INVALID_HANDLE) IndicatorRelease(stoch_handle);
    if(ema_handle != INVALID_HANDLE) IndicatorRelease(ema_handle);
    
    // Kill timer
    if(Enable_Auto_Refresh) {
        EventKillTimer();
    }
}

//+------------------------------------------------------------------+
//| Timer function for auto refresh                                  |
//+------------------------------------------------------------------+
void OnTimer()
{
    if(!Enable_Auto_Refresh) return;
    
    datetime current_time = TimeCurrent();
    datetime current_bar = iTime(Symbol(), Period(), 0);
    
    // Refresh on time interval or new bar
    if((current_time - last_refresh >= Refresh_Interval) || 
       (current_bar != last_bar)) {
        
        ChartRedraw();
        last_refresh = current_time;
        last_bar = current_bar;
    }
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    int start = prev_calculated;
    if(start < RSI_Period + Stoch_K_Period) start = RSI_Period + Stoch_K_Period;
    
    // Copy indicator data
    if(CopyBuffer(rsi_handle, 0, 0, rates_total, rsi_buffer) <= 0) return 0;
    if(CopyBuffer(stoch_handle, 0, 0, rates_total, stoch_main) <= 0) return 0;
    if(CopyBuffer(stoch_handle, 1, 0, rates_total, stoch_signal) <= 0) return 0;
    
    double ema_buffer[];
    bool has_trend_data = false;
    if(Use_Trend_Filter && ema_handle != INVALID_HANDLE) {
        ArrayResize(ema_buffer, rates_total);
        if(CopyBuffer(ema_handle, 0, 0, rates_total, ema_buffer) > 0) {
            has_trend_data = true;
        }
    }
    
    // Calculate optimized signals
    for(int i = start; i < rates_total; i++) {
        // Calculate base signal from RSI and Stochastic confluence
        double rsi_normalized = (rsi_buffer[i] - 50.0) / 50.0;  // Normalize RSI to -1 to +1
        double stoch_normalized = (stoch_main[i] - 50.0) / 50.0; // Normalize Stochastic
        
        // Calculate momentum factor
        double momentum = 0.0;
        if(i > 0) {
            momentum = (close[i] - close[i-1]) / close[i-1] * 1000; // Momentum in basis points
        }
        
        // Calculate volatility factor
        double volatility = 0.0;
        if(i > 0) {
            volatility = (high[i] - low[i]) / close[i] * 100; // Volatility as percentage
        }
        
        // Trend factor
        double trend_factor = 0.0;
        if(has_trend_data && i > 0) {
            trend_factor = (close[i] - ema_buffer[i]) / ema_buffer[i] * 100;
        }
        
        // Calculate composite signal with Jump75 optimization
        double base_signal = (rsi_normalized * 0.4 + stoch_normalized * 0.3 + 
                             momentum * 0.2 + trend_factor * 0.1) * Signal_Sensitivity;
        
        // Apply volatility filter
        double volatility_multiplier = 1.0;
        if(volatility > 0) {
            volatility_multiplier = MathMin(2.0, MathMax(0.5, Volatility_Filter / volatility));
        }
        
        signal_line[i] = base_signal * volatility_multiplier;
        
        // Generate buy/sell zones optimized for Jump75 scalping
        GenerateScalpingZones(i, signal_line[i], rsi_buffer[i], stoch_main[i], momentum, volatility);
    }
    
    return(rates_total);
}

//+------------------------------------------------------------------+
//| Generate optimized scalping zones for Jump75                     |
//+------------------------------------------------------------------+
void GenerateScalpingZones(int bar, double signal, double rsi, double stoch, double momentum, double volatility)
{
    // Initialize arrows
    buy_arrows[bar] = EMPTY_VALUE;
    sell_arrows[bar] = EMPTY_VALUE;
    buy_zone[bar] = EMPTY_VALUE;
    sell_zone[bar] = EMPTY_VALUE;

    // ULTRA-AGGRESSIVE: Only need 1 bar history for immediate response
    if(bar < 1) return;

    // Get price data
    double current_close = iClose(Symbol(), Period(), bar);
    double current_high = iHigh(Symbol(), Period(), bar);
    double current_low = iLow(Symbol(), Period(), bar);
    double prev_close = iClose(Symbol(), Period(), bar + 1);
    double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
    double pip_value = (point * 10 > 0.001) ? point * 10 : point;

    // IMMEDIATE price movement calculation
    double price_move = (current_close - prev_close) / pip_value;
    double candle_range = (current_high - current_low) / pip_value;

    // ULTRA-AGGRESSIVE SIGNAL CONDITIONS - NO TIME FILTERING
    // Ultra-loose volatility check
    bool good_volatility = (volatility >= MinVolatility && volatility <= MaxVolatility);

    if(good_volatility) {

        // ULTRA-AGGRESSIVE BUY SIGNAL CONDITIONS - TRIGGER ON ANY DOWNWARD MOVEMENT
        bool buy_signal = false;

        // IMMEDIATE price action: Any downward movement triggers buy
        bool price_down = current_close < prev_close;

        // ULTRA-LOOSE conditions - trigger on ANY downward movement
        if(price_down) {
            buy_signal = true;
        }

        // ULTRA-AGGRESSIVE SELL SIGNAL CONDITIONS - TRIGGER ON ANY UPWARD MOVEMENT
        bool sell_signal = false;

        // IMMEDIATE price action: Any upward movement triggers sell
        bool price_up = current_close > prev_close;

        // ULTRA-LOOSE conditions - trigger on ANY upward movement
        if(price_up) {
            sell_signal = true;
        }

        // IMMEDIATE SIGNAL GENERATION (No additional confluence needed)
        if(buy_signal) {
            buy_arrows[bar] = current_low - (2 * pip_value); // Fixed 2 pip distance
            buy_zone[bar] = signal;
        }

        if(sell_signal) {
            sell_arrows[bar] = current_high + (2 * pip_value); // Fixed 2 pip distance
            sell_zone[bar] = signal;
        }
    }

    // Set trend line for visualization
    trend_line[bar] = signal;
}

//+------------------------------------------------------------------+
//| Get signal for EA integration                                    |
//+------------------------------------------------------------------+
double GetSignalValue(int buffer_index, int shift = 0)
{
    switch(buffer_index) {
        case 0: return signal_line[shift];     // Main signal line
        case 1: return buy_zone[shift];        // Buy signals
        case 2: return sell_zone[shift];       // Sell signals
        case 3: return rsi_buffer[shift];      // RSI value
        case 4: return stoch_main[shift];      // Stochastic main
        case 5: return stoch_signal[shift];    // Stochastic signal
        default: return EMPTY_VALUE;
    }
}

//+------------------------------------------------------------------+
//| Check if buy signal is active                                    |
//+------------------------------------------------------------------+
bool IsBuySignal(int shift = 0)
{
    return (buy_zone[shift] != EMPTY_VALUE);
}

//+------------------------------------------------------------------+
//| Check if sell signal is active                                   |
//+------------------------------------------------------------------+
bool IsSellSignal(int shift = 0)
{
    return (sell_zone[shift] != EMPTY_VALUE);
}

//+------------------------------------------------------------------+
//| Get signal strength (0-100)                                      |
//+------------------------------------------------------------------+
double GetSignalStrength(int shift = 0)
{
    double strength = MathAbs(signal_line[shift]);
    return MathMin(100.0, strength * 2.0); // Scale to 0-100
}
