//+------------------------------------------------------------------+
//|                                      AMPD SELL Separate 2 .mq5   |
//|                                    Copyright 2023,  Ltd.AMPD1951 |
//|                                             https://www.ampd.com |
//+------------------------------------------------------------------+
#property copyright   "Arise Moroka Prince Dynasty"
#property link        "https://ampd.com"
#property description "WARNING!!! Use @ your Own Risk"
#property version     "V 12.0 ampd separate 2"

#property indicator_chart_window
#property indicator_buffers 2
#property indicator_plots   2
//--- plot Arrows
#property indicator_label1  "BuyArrow"
#property indicator_type1   DRAW_ARROW
#property indicator_color1  clrLime
#property indicator_width1  3
#property indicator_label2  "SellArrow"
#property indicator_type2   DRAW_ARROW
#property indicator_color2  clrRed
#property indicator_width2  3
//---
enum indicators
  {
   INDICATOR_STOCHASTIC,   //Stochastic Oscillator
   INDICATOR_RSI,          //Relative Strength Index
  };
//---
input indicators Indicator1=INDICATOR_STOCHASTIC;
input ENUM_TIMEFRAMES TimeFrame1=PERIOD_M10;
//---Range
input string Range;
input int RangePeriod=5;
//---Stoch
input string Stochastic;
input int Kperiod=2;
input int Dperiod=2;
input int Slowing=2;
input ENUM_MA_METHOD StochMAMethod=MODE_EMA;
input ENUM_STO_PRICE PriceField=STO_CLOSECLOSE;
//---RSI
input string RSI;
input int RSIPeriod=2;
input int RSISignal=2;
input ENUM_APPLIED_PRICE RSIPrice=PRICE_CLOSE;

int indicator1;
//---
double Buy[];
double Sell[];
//---
long chartID;
double pipToDouble;
int doubleToPip;
//---
int rangeHandle,
    indHandle1;
//---
MqlTick tick;

input int RefreshPeriod = 10;  // Refresh period in seconds
datetime last_refresh_time;

// Define signal constants
const int SIGNAL_NONE = 0;
const int SIGNAL_BUY = 1;
const int SIGNAL_SELL = 2;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
   IndicatorSetString(INDICATOR_SHORTNAME, "Arise Moroka Prince Dynasty");

   chartID = ChartID();

   // Set points & digits
   IndicatorSetInteger(INDICATOR_DIGITS, _Digits);   
   if(_Digits==2 || _Digits==3)  
      doubleToPip = 100;
   else                          
      doubleToPip = 10000;
   
   if(_Digits==2 || _Digits==4) 
      pipToDouble = _Point;
   else                       
      pipToDouble = _Point * 10;

   // Create indicator handles
   rangeHandle = iATR(NULL, 0, RangePeriod);
   if (rangeHandle == INVALID_HANDLE)
      return (INIT_FAILED);

   indHandle1 = INVALID_HANDLE;
   if (Indicator1 == INDICATOR_RSI)
      Alert("Indicator1 can't be 'No Indicator'");
   switch (Indicator1)
     {
      case INDICATOR_STOCHASTIC:
         indHandle1 = iStochastic(_Symbol, TimeFrame1, Kperiod, Dperiod, Slowing, StochMAMethod, PriceField);
         break;
      case INDICATOR_RSI:
         indHandle1 = iRSI(_Symbol, TimeFrame1, RSIPeriod, RSIPrice);
         break;
     }
   if (indHandle1 == INVALID_HANDLE)
      return (INIT_FAILED);

   // Indicator buffers mapping
   SetIndexBuffer(0, Buy, INDICATOR_DATA);
   ArraySetAsSeries(Buy, true);
   PlotIndexSetString(0, PLOT_LABEL, "BuyArrow");   
   PlotIndexSetInteger(0, PLOT_ARROW, 233);
   PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, 0);
   SetIndexBuffer(1, Sell, INDICATOR_DATA);
   ArraySetAsSeries(Sell, true);
   PlotIndexSetString(1, PLOT_LABEL, "SellArrow");   
   PlotIndexSetInteger(1, PLOT_ARROW, 234);
   PlotIndexSetDouble(1, PLOT_EMPTY_VALUE, 0);

   // Initialize the last refresh time
   last_refresh_time = TimeCurrent();
   
   // Set timer to trigger every second
   EventSetTimer(1);
   
   return (INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
  {
   // Kill the timer
   EventKillTimer();
   
   // Release indicator handles
   IndicatorRelease(rangeHandle);
   IndicatorRelease(indHandle1);
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
   if (rates_total < 100)  
      return 0;

   ArraySetAsSeries(time, true);
   ArraySetAsSeries(high, true);
   ArraySetAsSeries(low, true);

   int limit = rates_total - prev_calculated;
   if (prev_calculated == 0)
     { 
      limit = (int)ChartGetInteger(chartID, CHART_VISIBLE_BARS) + 100;
      PlotIndexSetInteger(0, PLOT_DRAW_BEGIN, rates_total - limit);
      PlotIndexSetInteger(1, PLOT_DRAW_BEGIN, rates_total - limit);
     }

   for (int i = limit; i >= 0 && !IsStopped(); i--)
     {
      static datetime Time[1];
      if (CopyTime(_Symbol, TimeFrame1, time[i], 1, Time) != 1)
         return 0;
      const datetime time1 = Time[0];
      switch (Indicator1)
        {
         case INDICATOR_STOCHASTIC:
           { 
            static double Stoc[2];
            static double Sign[2];
            if (CopyBuffer(indHandle1, 0, time1, 2, Stoc) != 2)
               return 0;
            if (CopyBuffer(indHandle1, 1, time1, 2, Sign) != 2)
               return 0;
            indicator1 = iStochastic(Stoc, Sign);
            break;
           }
         case INDICATOR_RSI:
           { 
            static double Rsi[];
            if (ArraySize(Rsi) != RSISignal + 1)
               ArrayResize(Rsi, RSISignal + 1);
            if (CopyBuffer(indHandle1, 0, time1, RSISignal + 1, Rsi) != RSISignal + 1)
               return 0;
            indicator1 = iRSI(Rsi);
            break;
           }
        }

      switch (indicator1)
        {
         case SIGNAL_BUY:
            Buy[i] = low[i] - pipToDouble * 5; // Place buy arrow below the low
            Sell[i] = 0; // Clear sell buffer

            // Send alert for new buy signal on current bar
            if(i == 0) {
               Alert("🟢 AMPD CHAT WINDOW BUY: ", Symbol(), " | Price: ", DoubleToString(close[i], 5),
                     " | Time: ", TimeToString(time[i], TIME_MINUTES));
            }
            break;

         case SIGNAL_SELL:
            Sell[i] = high[i] + pipToDouble * 5; // Place sell arrow above the high
            Buy[i] = 0; // Clear buy buffer

            // Send alert for new sell signal on current bar
            if(i == 0) {
               Alert("🔴 AMPD CHAT WINDOW SELL: ", Symbol(), " | Price: ", DoubleToString(close[i], 5),
                     " | Time: ", TimeToString(time[i], TIME_MINUTES));
            }
            break;

         default:
            Buy[i] = 0;
            Sell[i] = 0;
            break;
        }
     }
   return rates_total;
  }
//+------------------------------------------------------------------+
//| Timer function                                                   |
//+------------------------------------------------------------------+
void OnTimer()
  {
   datetime current_time = TimeCurrent();
   
   // Check if it's time to refresh
   if (current_time - last_refresh_time >= RefreshPeriod)
     {
      // Refresh the chart
      RefreshChart();
      
      // Update the last refresh time
      last_refresh_time = current_time;
     }
  }
//+------------------------------------------------------------------+
//| Refresh the chart function                                       |
//+------------------------------------------------------------------+
void RefreshChart()
  {
   // Refresh the chart
   ChartRedraw(chartID);
  }
//+------------------------------------------------------------------+
//| Average Range                                                    |
//+------------------------------------------------------------------+
double Range(int idx)
  {
   static double range[1];
   if (CopyBuffer(rangeHandle, 0, idx + 1, 1, range) != 1)
      return 0.0;
   double avgRange = range[0];
   return avgRange;
  }
//+------------------------------------------------------------------+
//| ULTRA-AGGRESSIVE Stochastic Oscillator                          |
//+------------------------------------------------------------------+
int iStochastic(const double &stoc[], const double &sign[])
  {
   double currStoc = stoc[1];
   double prevStoc = stoc[0];
   double currSign = sign[1];
   double prevSign = sign[0];

   int signal = SIGNAL_NONE;

   // ULTRA-AGGRESSIVE: Trigger on ANY crossover or movement
   if (currStoc > currSign && prevStoc < prevSign)
      signal = SIGNAL_BUY;
   if (currStoc < currSign && prevStoc > prevSign)
      signal = SIGNAL_SELL;

   // Additional ultra-aggressive conditions - trigger on ANY movement
   if (currStoc < 50 && currStoc > prevStoc)  // Any upward movement in oversold
      signal = SIGNAL_BUY;
   if (currStoc > 50 && currStoc < prevStoc)  // Any downward movement in overbought
      signal = SIGNAL_SELL;

   return signal;
  }
//+------------------------------------------------------------------+
//| ULTRA-AGGRESSIVE Relative Strength Index                        |
//+------------------------------------------------------------------+
int iRSI(const double &rsi[])
  {
   double sum1 = 0.0;
   double sum2 = 0.0;
   for (int i = 0; i < RSISignal; i++)
     {
      sum1 += rsi[i];
      sum2 += rsi[i + 1];
     }

   double avg1 = sum1 / RSISignal;
   double avg2 = sum2 / RSISignal;

   int signal = SIGNAL_NONE;

   // ULTRA-AGGRESSIVE: Trigger on ANY RSI movement
   if (avg1 > avg2)
      signal = SIGNAL_BUY;
   if (avg1 < avg2)
      signal = SIGNAL_SELL;

   // Additional ultra-aggressive conditions - trigger on ANY RSI level
   if (avg1 < 50)  // Any RSI below 50 triggers buy
      signal = SIGNAL_BUY;
   if (avg1 > 50)  // Any RSI above 50 triggers sell
      signal = SIGNAL_SELL;

   return signal;
  }
//+------------------------------------------------------------------+
//| Alert                                                            |
//+------------------------------------------------------------------+
void AlertsHandle(datetime T, int Signal)
  {
   MqlDateTime tm;
   TimeToStruct(T, tm);
   string Txt = StringFormat("%s %02d/%02d/%d %02d:%02d Signal: %s", _Symbol, tm.mon, tm.day, tm.year, tm.hour, tm.min, Signal == 1 ? "BUY" : "SELL");
   if (Signal == SIGNAL_BUY) 
      Alert("Buy: ", Txt);
   if (Signal == SIGNAL_SELL) 
      Alert("Sell: ", Txt);
  }

// Function to calculate ULTRA-AGGRESSIVE Stochastic signals
int iStochastic(const double &Stoc[], const double &Sign[])
{
    // ULTRA-AGGRESSIVE: Trigger on ANY movement with very loose conditions
    if (Stoc[0] < 50 && Stoc[0] > Sign[0])  // Very loose oversold (50 instead of 20)
        return SIGNAL_BUY;
    if (Stoc[0] > 50 && Stoc[0] < Sign[0])  // Very loose overbought (50 instead of 80)
        return SIGNAL_SELL;

    // Additional ultra-aggressive conditions - trigger on ANY crossover
    if (Stoc[0] > Sign[0])
        return SIGNAL_BUY;
    if (Stoc[0] < Sign[0])
        return SIGNAL_SELL;

    return SIGNAL_NONE;
}

// Function to calculate ULTRA-AGGRESSIVE RSI signals
int iRSI(const double &Rsi[])
{
    // ULTRA-AGGRESSIVE: Trigger on ANY RSI movement with very loose conditions
    if (Rsi[0] < 50)  // Very loose oversold (50 instead of 30)
        return SIGNAL_BUY;
    if (Rsi[0] > 50)  // Very loose overbought (50 instead of 70)
        return SIGNAL_SELL;

    return SIGNAL_NONE;
}
