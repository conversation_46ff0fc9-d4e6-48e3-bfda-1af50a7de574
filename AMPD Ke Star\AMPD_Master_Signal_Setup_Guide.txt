🎯 AMPD MASTER SIGNAL SYSTEM - SETUP GUIDE
═══════════════════════════════════════════════════════════════

📋 SYSTEM OVERVIEW
═══════════════════
✅ ONE MAIN CURRENT REAL-TIME SIGNAL for all systems
✅ Master Signal Controller coordinates all indicators
✅ Ultra-fast refresh rates (1-second intervals)
✅ Optimized for Jump75 Index M1 scalping
✅ Unified EA that executes based on master signals

📁 FILES INCLUDED
═══════════════════
1. AMPD_Master_Signal_Controller.mq5     - Main signal coordinator
2. AMPD_Jump75_Unified_Optimized.mq5     - Updated EA with master signal integration
3. AMPD_G65_V2_Optimized.mq5             - Optimized indicator (ultra-sensitive)
4. AMPD_System_Test.mq5                  - System testing tool
5. This setup guide

🚀 INSTALLATION STEPS
═══════════════════════

STEP 1: Compile Indicators
─────────────────────────
1. Open MetaEditor
2. Compile AMPD_Master_Signal_Controller.mq5
3. Compile AMPD_G65_V2_Optimized.mq5
4. Compile AMPD_System_Test.mq5
5. Ensure no compilation errors

STEP 2: Compile EA
─────────────────────
1. Compile AMPD_Jump75_Unified_Optimized.mq5
2. Ensure no compilation errors

STEP 3: Setup Chart
─────────────────────
1. Open Jump75 Index chart
2. Set timeframe to M1 (1-minute)
3. Add AMPD_Master_Signal_Controller indicator first
4. Add AMPD_G65_V2_Optimized indicator
5. Attach AMPD_Jump75_Unified_Optimized EA

⚙️ OPTIMAL SETTINGS
═══════════════════

MASTER SIGNAL CONTROLLER:
─────────────────────────
✅ EnableMasterSignal = true
✅ SignalValidityBars = 3
✅ SignalStrength = 1.0
✅ ShowOnlyCurrentSignal = true
✅ EnableRealTimeRefresh = true
✅ RefreshIntervalMs = 100
✅ ForceChartRedraw = true

AMPD G65 V2.0 OPTIMIZED:
─────────────────────────
✅ inpFastLength = 1
✅ inpSlowLength = 1
✅ inpSignal = 1.1 (ULTRA-SENSITIVE)
✅ inpRange = 1.2 (ULTRA-SENSITIVE)
✅ inpPeriod = 1.1
✅ MinPipMove = 0.3 (ULTRA-SENSITIVE)
✅ RefreshIntervalSeconds = 1

UNIFIED EA SETTINGS:
─────────────────────
✅ UseMasterSignalController = true
✅ OnlyTradeMasterSignals = true
✅ MasterSignalValiditySeconds = 60
✅ ScalpingTimeframe = PERIOD_M1
✅ TargetSignalFrequency = 1
✅ ScalpingPipTarget = 2.0
✅ ScalpingStopLoss = 5.0
✅ EnableAutoRefresh = true
✅ RefreshIntervalSeconds = 1

🎯 SIGNAL BEHAVIOR
═══════════════════

MASTER SIGNAL LOGIC:
─────────────────────
🟢 BUY SIGNAL: Triggered on ANY downward price movement
🔴 SELL SIGNAL: Triggered on ANY upward price movement
⚡ REAL-TIME: Updates every 100ms for immediate response
🎯 ONE SIGNAL: Only shows current active signal
📊 STRENGTH: Calculated based on momentum and volatility

ARROW DISPLAY:
─────────────────
🟢 Lime arrows = BUY signals (below candle)
🔴 Red arrows = SELL signals (above candle)
⚡ Arrows appear on EVERY qualifying candle
🎯 Master signal overrides all other signals

🧪 TESTING PROCEDURE
═══════════════════════

STEP 1: Run System Test
─────────────────────────
1. Add AMPD_System_Test indicator to chart
2. Set TestDurationMinutes = 10
3. Enable all test options
4. Monitor console for signal detection

STEP 2: Verify Signal Generation
─────────────────────────────────
1. Watch for frequent lime/red arrows
2. Check console for "MASTER SIGNAL DETECTED" messages
3. Verify EA responds to master signals only
4. Confirm real-time refresh working

STEP 3: Monitor Performance
─────────────────────────────
1. Check Interactive AI Dashboard
2. Monitor "MASTER" status in dashboard
3. Verify signal frequency (every 1-5 minutes)
4. Check trade execution on signals

📊 EXPECTED RESULTS
═══════════════════

SIGNAL FREQUENCY:
─────────────────
✅ Signals every 1-5 minutes (ultra-frequent)
✅ Arrows on most candles with movement
✅ Master signal coordinates all systems
✅ Real-time updates without delays

TRADING BEHAVIOR:
─────────────────
✅ EA only trades on master signals
✅ Immediate execution on signal detection
✅ 2-pip targets, 5-pip stops (scalping)
✅ High-frequency trading opportunities

🔧 TROUBLESHOOTING
═══════════════════

PROBLEM: No arrows appearing
SOLUTION: Check MinPipMove setting (reduce to 0.1)

PROBLEM: EA not trading
SOLUTION: Verify UseMasterSignalController = true

PROBLEM: Slow updates
SOLUTION: Check RefreshIntervalSeconds = 1

PROBLEM: Too many signals
SOLUTION: Increase MinPipMove to 0.5

PROBLEM: No master signals
SOLUTION: Recompile and reload Master Signal Controller

📈 OPTIMIZATION TIPS
═══════════════════

FOR MORE SIGNALS:
─────────────────
• Reduce MinPipMove to 0.1
• Set inpSignal to 1.0
• Enable ForceRealTimeUpdate

FOR FEWER SIGNALS:
─────────────────
• Increase MinPipMove to 0.5
• Set inpSignal to 1.5
• Add volatility filters

FOR FASTER RESPONSE:
─────────────────────
• Set RefreshIntervalMs to 50
• Enable all real-time options
• Use PERIOD_M1 only

🎯 FINAL NOTES
═══════════════

✅ System designed for ONE MAIN CURRENT SIGNAL
✅ All indicators coordinate through Master Signal Controller
✅ Ultra-fast refresh ensures real-time accuracy
✅ Optimized specifically for Jump75 Index M1 scalping
✅ Arrows provide visual confirmation of signal generation

🚀 The system is now ready for live trading with maximum signal accuracy and frequency!

═══════════════════════════════════════════════════════════════
