//+------------------------------------------------------------------+
//|                                        AMPD G65 V2.0 Optimized  |
//|                        Copyright 2025, AMPD Trading Systems      |
//|                   Optimized for Jump75 Index M1 Scalping        |
//+------------------------------------------------------------------+
#property copyright "AMPD Trading Systems"
#property link      "https://ampd.com"
#property description "AMPD G65 V2.0 - Optimized for Jump75 scalping"
#property version   "2.1"
#property indicator_chart_window
#property indicator_buffers 9
#property indicator_plots   2

#property indicator_label1     "AMPD Buy"
#property indicator_type1      DRAW_ARROW                       
#property indicator_color1     clrLime
#property indicator_width1     3

#property indicator_label2     "AMPD Sell"
#property indicator_type2      DRAW_ARROW
#property indicator_color2     clrRed
#property indicator_width2     3

//--- Input parameters optimized for Jump75 scalping
input group "=== AMPD G65 OPTIMIZATION ==="
input int inpFastLength = 5;        // Fast length (optimized for M1 scalping)
input int inpSlowLength = 14;       // Slow length (optimized for volatility)
input double inpSignal = 1.2;       // Signal threshold (reduced for more signals)
input double inpRange = 1.8;        // Range multiplier (optimized for Jump75)
input double inpPeriod = 2.5;       // Period multiplier (enhanced sensitivity)

input group "=== AUTO REFRESH SETTINGS ==="
input bool EnableAutoRefresh = true;     // Enable automatic refresh
input int RefreshIntervalSeconds = 5;    // Refresh interval in seconds

//--- Indicator buffers
double slowlu[], slowld[], slowln[], fastln[], fastcl[], arrowcl[], arrowcl_prev[], trend[], signal_buffer[];

//--- Global variables for auto refresh
datetime last_refresh_time = 0;
datetime last_bar_time = 0;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
    // Set indicator buffers
    SetIndexBuffer(0, slowlu, INDICATOR_DATA);
    SetIndexBuffer(1, slowld, INDICATOR_DATA);
    SetIndexBuffer(2, fastln, INDICATOR_DATA);
    SetIndexBuffer(3, fastcl, INDICATOR_COLOR_INDEX);
    SetIndexBuffer(4, arrowcl, INDICATOR_COLOR_INDEX);
    SetIndexBuffer(5, trend, INDICATOR_CALCULATIONS);
    SetIndexBuffer(6, slowln, INDICATOR_CALCULATIONS);
    SetIndexBuffer(7, arrowcl_prev, INDICATOR_COLOR_INDEX);
    SetIndexBuffer(8, signal_buffer, INDICATOR_CALCULATIONS);
    
    // Set arrow codes
    PlotIndexSetInteger(0, PLOT_ARROW, 233);  // Up arrow for buy
    PlotIndexSetInteger(1, PLOT_ARROW, 234);  // Down arrow for sell
    
    // Initialize arrays
    ArrayInitialize(arrowcl_prev, 0);
    ArrayInitialize(signal_buffer, 0);
    
    // Set indicator name
    IndicatorSetString(INDICATOR_SHORTNAME, "AMPD G65 V2.1 Optimized");
    
    // Initialize refresh timer
    if(EnableAutoRefresh) {
        EventSetTimer(1); // Check every second
    }
    
    Print("AMPD G65 V2.0 Optimized initialized for Jump75 scalping");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) 
{ 
    if(EnableAutoRefresh) {
        EventKillTimer();
    }
    return; 
}

//+------------------------------------------------------------------+
//| Timer function for auto refresh                                  |
//+------------------------------------------------------------------+
void OnTimer()
{
    if(!EnableAutoRefresh) return;
    
    datetime current_time = TimeCurrent();
    datetime current_bar_time = iTime(Symbol(), Period(), 0);
    
    // Check if it's time to refresh or new bar
    if((current_time - last_refresh_time >= RefreshIntervalSeconds) || 
       (current_bar_time != last_bar_time)) {
        
        // Force indicator recalculation
        ChartRedraw();
        
        last_refresh_time = current_time;
        last_bar_time = current_bar_time;
    }
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{                                 
    int start = prev_calculated - 1; 
    if(start < 0) start = 0; 
    
    for(int i = start; i < rates_total && !_StopFlag; i++) {
        // Calculate fast and slow ranges
        int _startf = i - inpFastLength + 1; 
        if(_startf < 0) _startf = 0;
        
        int _starts = i - inpSlowLength + 1; 
        if(_starts < 0) _starts = 0;
        
        // Get highest and lowest values for slow period
        double thighs = high[ArrayMaximum(high, _starts, inpSlowLength)];
        double tlows = low[ArrayMinimum(low, _starts, inpSlowLength)];
        
        // Get highest and lowest values for fast period
        double thighf = high[ArrayMaximum(high, _startf, inpFastLength)];
        double tlowf = low[ArrayMinimum(low, _startf, inpFastLength)];
        
        if(i > 0) {
            // Calculate adaptive levels with optimized logic
            slowln[i] = (close[i] > slowln[i-1]) ? tlows : thighs;
            fastln[i] = (close[i] > fastln[i-1]) ? tlowf : thighf;
            
            // Enhanced trend detection for Jump75
            trend[i] = trend[i-1];
            
            // Optimized signal conditions for scalping
            double signal_strength = MathAbs(close[i] - slowln[i]) / (thighs - tlows + 0.0001);
            double volatility_factor = (thighs - tlows) / close[i];
            
            // Buy signal conditions (optimized for Jump75)
            if(close[i] < slowln[i] && close[i] < fastln[i] && 
               signal_strength > inpSignal/100.0 && volatility_factor > inpRange/1000.0) {
                trend[i] = 1; // Bearish trend (for contrarian signals)
            }
            
            // Sell signal conditions (optimized for Jump75)
            if(close[i] > slowln[i] && close[i] > fastln[i] && 
               signal_strength > inpSignal/100.0 && volatility_factor > inpRange/1000.0) {
                trend[i] = 0; // Bullish trend (for contrarian signals)
            }
            
            // Set buffer values for display
            slowlu[i] = (trend[i] == 0) ? slowln[i] : EMPTY_VALUE;
            slowld[i] = (trend[i] == 1) ? slowln[i] : EMPTY_VALUE;
            
            // Enhanced signal generation with period optimization
            double period_factor = inpPeriod / 10.0;
            signal_buffer[i] = signal_strength * period_factor;
            
        } else {
            // Initialize first values
            slowlu[i] = slowld[i] = EMPTY_VALUE;
            trend[i] = fastcl[i] = arrowcl[i] = 0;
            fastln[i] = slowln[i] = close[i];
            signal_buffer[i] = 0;
        }
        
        // Set color indices for arrows
        fastcl[i] = arrowcl[i] = trend[i];
        
        // Generate optimized trading signals
        GenerateOptimizedSignals(i, rates_total);
    }
    
    return(rates_total);
}

//+------------------------------------------------------------------+
//| Generate optimized trading signals for Jump75                    |
//+------------------------------------------------------------------+
void GenerateOptimizedSignals(int current_bar, int total_bars)
{
    if(current_bar < 2) return;
    
    // Clear previous arrows
    slowlu[current_bar] = EMPTY_VALUE;
    slowld[current_bar] = EMPTY_VALUE;
    
    // Enhanced signal logic for Jump75 scalping
    bool buy_signal = false;
    bool sell_signal = false;
    
    // Get current and previous values
    double current_close = iClose(Symbol(), Period(), total_bars - current_bar - 1);
    double prev_close = iClose(Symbol(), Period(), total_bars - current_bar);
    double current_high = iHigh(Symbol(), Period(), total_bars - current_bar - 1);
    double current_low = iLow(Symbol(), Period(), total_bars - current_bar - 1);
    
    // Calculate momentum and volatility
    double momentum = (current_close - prev_close) / prev_close;
    double candle_range = (current_high - current_low) / current_close;
    
    // Optimized buy signal (contrarian approach for Jump75)
    if(trend[current_bar] == 1 && trend[current_bar-1] == 0) {
        if(signal_buffer[current_bar] > inpSignal/100.0 && 
           momentum < -0.001 && candle_range > inpRange/1000.0) {
            buy_signal = true;
        }
    }
    
    // Optimized sell signal (contrarian approach for Jump75)
    if(trend[current_bar] == 0 && trend[current_bar-1] == 1) {
        if(signal_buffer[current_bar] > inpSignal/100.0 && 
           momentum > 0.001 && candle_range > inpRange/1000.0) {
            sell_signal = true;
        }
    }
    
    // Additional confirmation for scalping
    if(buy_signal || sell_signal) {
        // Check for minimum volatility
        double atr = CalculateATR(current_bar, 14);
        if(atr < 0.0001) return; // Too low volatility
        if(atr > 0.005) return;  // Too high volatility
        
        // Set arrow signals
        if(buy_signal) {
            slowlu[current_bar] = current_low - (atr * 2);
        }
        if(sell_signal) {
            slowld[current_bar] = current_high + (atr * 2);
        }
    }
}

//+------------------------------------------------------------------+
//| Calculate ATR for volatility filtering                           |
//+------------------------------------------------------------------+
double CalculateATR(int bar, int period)
{
    if(bar < period) return 0.0;
    
    double atr_sum = 0.0;
    for(int i = 0; i < period; i++) {
        double high_val = iHigh(Symbol(), Period(), i);
        double low_val = iLow(Symbol(), Period(), i);
        double prev_close = iClose(Symbol(), Period(), i + 1);
        
        double tr = MathMax(high_val - low_val, 
                   MathMax(MathAbs(high_val - prev_close), 
                          MathAbs(low_val - prev_close)));
        atr_sum += tr;
    }
    
    return atr_sum / period;
}

//+------------------------------------------------------------------+
//| Get signal value for EA integration                              |
//+------------------------------------------------------------------+
double GetSignalValue(int buffer_index, int shift = 0)
{
    if(buffer_index == 0) return slowlu[shift];  // Buy signals
    if(buffer_index == 1) return slowld[shift];  // Sell signals
    if(buffer_index == 2) return signal_buffer[shift]; // Signal strength
    return EMPTY_VALUE;
}
