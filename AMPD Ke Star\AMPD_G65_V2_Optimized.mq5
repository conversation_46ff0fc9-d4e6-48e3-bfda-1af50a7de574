//+------------------------------------------------------------------+
//|                                        AMPD G65 V2.0 Optimized  |
//|                        Copyright 2025, AMPD Trading Systems      |
//|                   Optimized for Jump75 Index M1 Scalping        |
//+------------------------------------------------------------------+
#property copyright "AMPD Trading Systems"
#property link      "https://ampd.com"
#property description "AMPD G65 V2.0 - Optimized for Jump75 scalping"
#property version   "2.1"
#property indicator_chart_window
#property indicator_buffers 9
#property indicator_plots   2

#property indicator_label1     "AMPD Buy"
#property indicator_type1      DRAW_ARROW                       
#property indicator_color1     clrLime
#property indicator_width1     3

#property indicator_label2     "AMPD Sell"
#property indicator_type2      DRAW_ARROW
#property indicator_color2     clrRed
#property indicator_width2     3

//--- Input parameters ULTRA-AGGRESSIVE for immediate scalping
input group "=== AMPD G65 ULTRA-AGGRESSIVE SCALPING ==="
input int inpFastLength = 1;        // Fast length (IMMEDIATE response)
input int inpSlowLength = 1;        // Slow length (IMMEDIATE response)
input double inpSignal = 1.7;       // Signal threshold (from template)
input double inpRange = 1.5;        // Range multiplier (from template)
input double inpPeriod = 1.1;       // Period multiplier (from template)
input int SignalFrequency = 1;      // Target signals every 1 minute (ultra-frequent)
input double MinPipMove = 0.5;      // Minimum pip movement (ultra-sensitive)

input group "=== AUTO REFRESH SETTINGS ==="
input bool EnableAutoRefresh = true;     // Enable automatic refresh
input int RefreshIntervalSeconds = 5;    // Refresh interval in seconds

//--- Indicator buffers
double slowlu[], slowld[], slowln[], fastln[], fastcl[], arrowcl[], arrowcl_prev[], trend[], signal_buffer[];

//--- Global variables for auto refresh
datetime last_refresh_time = 0;
datetime last_bar_time = 0;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
    // Set indicator buffers
    SetIndexBuffer(0, slowlu, INDICATOR_DATA);
    SetIndexBuffer(1, slowld, INDICATOR_DATA);
    SetIndexBuffer(2, fastln, INDICATOR_DATA);
    SetIndexBuffer(3, fastcl, INDICATOR_COLOR_INDEX);
    SetIndexBuffer(4, arrowcl, INDICATOR_COLOR_INDEX);
    SetIndexBuffer(5, trend, INDICATOR_CALCULATIONS);
    SetIndexBuffer(6, slowln, INDICATOR_CALCULATIONS);
    SetIndexBuffer(7, arrowcl_prev, INDICATOR_COLOR_INDEX);
    SetIndexBuffer(8, signal_buffer, INDICATOR_CALCULATIONS);
    
    // Set arrow codes
    PlotIndexSetInteger(0, PLOT_ARROW, 233);  // Up arrow for buy
    PlotIndexSetInteger(1, PLOT_ARROW, 234);  // Down arrow for sell
    
    // Initialize arrays
    ArrayInitialize(arrowcl_prev, 0);
    ArrayInitialize(signal_buffer, 0);
    
    // Set indicator name
    IndicatorSetString(INDICATOR_SHORTNAME, "AMPD G65 V2.1 Optimized");
    
    // Initialize refresh timer
    if(EnableAutoRefresh) {
        EventSetTimer(1); // Check every second
    }
    
    Print("AMPD G65 V2.0 Optimized initialized for Jump75 scalping");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) 
{ 
    if(EnableAutoRefresh) {
        EventKillTimer();
    }
    return; 
}

//+------------------------------------------------------------------+
//| Timer function for auto refresh                                  |
//+------------------------------------------------------------------+
void OnTimer()
{
    if(!EnableAutoRefresh) return;
    
    datetime current_time = TimeCurrent();
    datetime current_bar_time = iTime(Symbol(), Period(), 0);
    
    // Check if it's time to refresh or new bar
    if((current_time - last_refresh_time >= RefreshIntervalSeconds) || 
       (current_bar_time != last_bar_time)) {
        
        // Force indicator recalculation
        ChartRedraw();
        
        last_refresh_time = current_time;
        last_bar_time = current_bar_time;
    }
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{                                 
    int start = prev_calculated - 1; 
    if(start < 0) start = 0; 
    
    for(int i = start; i < rates_total && !_StopFlag; i++) {
        // Calculate fast and slow ranges with bounds checking
        int _startf = MathMax(0, i - inpFastLength + 1);
        int _starts = MathMax(0, i - inpSlowLength + 1);

        // Ensure we have enough data
        if(i < inpSlowLength || i < inpFastLength) {
            slowlu[i] = slowld[i] = EMPTY_VALUE;
            trend[i] = fastcl[i] = arrowcl[i] = 0;
            fastln[i] = slowln[i] = close[i];
            signal_buffer[i] = 0;
            continue;
        }

        // Get highest and lowest values for slow period
        double thighs = high[ArrayMaximum(high, _starts, inpSlowLength)];
        double tlows = low[ArrayMinimum(low, _starts, inpSlowLength)];

        // Get highest and lowest values for fast period
        double thighf = high[ArrayMaximum(high, _startf, inpFastLength)];
        double tlowf = low[ArrayMinimum(low, _startf, inpFastLength)];
        
        // ULTRA-AGGRESSIVE SIGNAL GENERATION - IMMEDIATE RESPONSE
        slowlu[i] = EMPTY_VALUE;
        slowld[i] = EMPTY_VALUE;

        if(i >= 1) { // Only need 1 bar history for immediate response
            // Calculate price movement and volatility
            double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
            double pip_value = (point * 10 > 0.001) ? point * 10 : point;
            double price_change = (close[i] - close[i-1]) / pip_value;
            double volatility = (high[i] - low[i]) / pip_value;

            // IMMEDIATE momentum calculation
            double momentum_1 = close[i] - close[i-1];

            // Calculate adaptive levels with IMMEDIATE response
            slowln[i] = (close[i] > close[i-1]) ? tlows : thighs;
            fastln[i] = (close[i] > close[i-1]) ? tlowf : thighf;

            // ULTRA-AGGRESSIVE BUY SIGNAL CONDITIONS
            bool buy_signal = false;
            if(volatility >= MinPipMove) { // Any volatility above minimum
                // IMMEDIATE price action conditions
                bool price_down = close[i] < close[i-1]; // Any downward movement
                bool oversold_level = close[i] < slowln[i]; // Below adaptive level
                bool momentum_positive = momentum_1 >= 0; // Any positive momentum

                // Signal strength based on template parameters
                double signal_strength = MathAbs(price_change) * inpSignal;
                bool strong_signal = signal_strength >= inpRange;

                if(price_down && oversold_level && (momentum_positive || strong_signal)) {
                    buy_signal = true;
                }
            }

            // ULTRA-AGGRESSIVE SELL SIGNAL CONDITIONS
            bool sell_signal = false;
            if(volatility >= MinPipMove) { // Any volatility above minimum
                // IMMEDIATE price action conditions
                bool price_up = close[i] > close[i-1]; // Any upward movement
                bool overbought_level = close[i] > slowln[i]; // Above adaptive level
                bool momentum_negative = momentum_1 <= 0; // Any negative momentum

                // Signal strength based on template parameters
                double signal_strength = MathAbs(price_change) * inpSignal;
                bool strong_signal = signal_strength >= inpRange;

                if(price_up && overbought_level && (momentum_negative || strong_signal)) {
                    sell_signal = true;
                }
            }

            // IMMEDIATE SIGNAL GENERATION (No time filtering for ultra-aggressive)
            static datetime last_signal_time = 0;
            datetime current_time = iTime(Symbol(), Period(), i);

            // Allow signals every minute for ultra-frequent trading
            if(current_time - last_signal_time >= SignalFrequency * 60) {
                if(buy_signal) {
                    slowlu[i] = low[i] - (2 * pip_value); // Fixed 2 pip distance
                    trend[i] = 1;
                    last_signal_time = current_time;
                } else if(sell_signal) {
                    slowld[i] = high[i] + (2 * pip_value); // Fixed 2 pip distance
                    trend[i] = 0;
                    last_signal_time = current_time;
                } else {
                    trend[i] = (i > 0) ? trend[i-1] : 0;
                }
            } else {
                trend[i] = (i > 0) ? trend[i-1] : 0;
            }

            signal_buffer[i] = volatility * inpPeriod; // Apply period multiplier
        } else {
            // Initialize early values
            slowln[i] = close[i];
            fastln[i] = close[i];
            trend[i] = 0;
            signal_buffer[i] = 0;
        }
        
        // Set color indices for arrows
        fastcl[i] = arrowcl[i] = trend[i];
    }
    
    return(rates_total);
}



//+------------------------------------------------------------------+
//| Calculate ATR for volatility filtering                           |
//+------------------------------------------------------------------+
double CalculateATR(int bar, int period)
{
    if(bar < period) return 0.0;
    
    double atr_sum = 0.0;
    for(int i = 0; i < period; i++) {
        double high_val = iHigh(Symbol(), Period(), i);
        double low_val = iLow(Symbol(), Period(), i);
        double prev_close = iClose(Symbol(), Period(), i + 1);
        
        double tr = MathMax(high_val - low_val, 
                   MathMax(MathAbs(high_val - prev_close), 
                          MathAbs(low_val - prev_close)));
        atr_sum += tr;
    }
    
    return atr_sum / period;
}

//+------------------------------------------------------------------+
//| Get signal value for EA integration                              |
//+------------------------------------------------------------------+
double GetSignalValue(int buffer_index, int shift = 0)
{
    if(buffer_index == 0) return slowlu[shift];  // Buy signals
    if(buffer_index == 1) return slowld[shift];  // Sell signals
    if(buffer_index == 2) return signal_buffer[shift]; // Signal strength
    return EMPTY_VALUE;
}
