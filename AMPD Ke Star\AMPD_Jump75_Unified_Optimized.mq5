//+------------------------------------------------------------------+
//|                         AMPD Jump75 Unified Optimized Trading EA |
//|                        Copyright 2025, AMPD Trading Systems      |
//|                   Optimized for Loss Elimination & High Win Rate |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, AMPD Trading Systems"
#property link      "https://AMPD.com"
#property version   "2.0"
#property description "Unified Buy/Sell EA optimized for Jump75 scalping with loss elimination"

// Include necessary libraries
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\OrderInfo.mqh>

// Create trade objects
CTrade trade;
CPositionInfo positionInfo;
COrderInfo orderInfo;

//--- Input parameters optimized for scalping
input group "=== TRADING SETTINGS ==="
input double LotSize = 0.5;                    // Base lot size (from trade history)
input double MaxLotSize = 2.0;                 // Maximum lot size
input int MagicNumber = 4534;                  // Magic number (from original EAs)
input int Slippage = 3;                        // Slippage in points

input group "=== SCALPING OPTIMIZATION ==="
input ENUM_TIMEFRAMES ScalpingTimeframe = PERIOD_M1;  // Primary scalping timeframe
input bool UseMultiTimeframeConfirmation = true;      // Use higher TF confirmation
input ENUM_TIMEFRAMES ConfirmationTimeframe = PERIOD_M5; // Confirmation timeframe
input int MinPipDistance = 5;                         // Minimum pip distance between trades
input int MaxTradesPerHour = 10;                      // Maximum trades per hour limit

input group "=== LOSS ELIMINATION FILTERS ==="
input bool UseVolatilityFilter = true;        // Enable volatility filter
input double MinVolatilityATR = 0.0001;        // Minimum ATR for trading
input double MaxVolatilityATR = 0.0050;        // Maximum ATR for trading
input bool UseSpreadFilter = true;             // Enable spread filter
input double MaxSpreadPips = 2.0;              // Maximum spread in pips
input bool UseTimeFilter = true;               // Enable time-based filter
input int StartHour = 8;                       // Trading start hour (London open)
input int EndHour = 16;                        // Trading end hour (NY close)
input bool AvoidNewsTime = true;               // Avoid high-impact news times

input group "=== SIGNAL OPTIMIZATION ==="
input bool UseCustomIndicators = true;        // Use AMPD custom indicators
input int RSIPeriod = 14;                      // RSI period for momentum
input double RSIOverbought = 70.0;             // RSI overbought level
input double RSIOversold = 30.0;               // RSI oversold level
input int EMAPeriod = 21;                      // EMA period for trend
input bool UseStochasticFilter = true;         // Use Stochastic filter
input int StochKPeriod = 14;                   // Stochastic %K period
input int StochDPeriod = 3;                    // Stochastic %D period

input group "=== AMPD CUSTOM INDICATOR SETTINGS ==="
input int AMPD_G65_FastLength = 5;             // AMPD G65 Fast Length (optimized for M1)
input int AMPD_G65_SlowLength = 14;            // AMPD G65 Slow Length
input double AMPD_G65_Signal = 1.2;            // AMPD G65 Signal threshold
input double AMPD_G65_Range = 1.8;             // AMPD G65 Range multiplier
input double AMPD_G65_Period = 2.5;            // AMPD G65 Period multiplier
input int AMPD_75s_RSI_Period = 8;             // AMPD 75s RSI Period
input int AMPD_75s_RSI_Signal = 12;            // AMPD 75s RSI Signal Level
input int AMPD_75s_Stoch_K = 8;                // AMPD 75s Stochastic K
input int AMPD_75s_Stoch_D = 3;                // AMPD 75s Stochastic D
input double AMPD_Signal_Sensitivity = 1.5;    // AMPD Signal Sensitivity

input group "=== AUTO REFRESH SETTINGS ==="
input bool EnableAutoRefresh = true;          // Enable auto refresh for real-time data
input int RefreshIntervalSeconds = 3;         // Refresh interval in seconds
input bool RefreshOnNewTick = true;           // Refresh on every new tick
input bool RefreshOnNewBar = true;            // Refresh on new bar

input group "=== ADVANCED MARTINGALE ==="
input bool UseMartingale = true;               // Use martingale system
input string LotProgression = "0.5,0.7,1.0,1.4,2.0"; // Lot progression
input int MaxMartingaleLevels = 4;             // Maximum martingale levels
input double MartingaleResetProfit = 10.0;     // Profit to reset martingale

input group "=== RISK MANAGEMENT ==="
input double DailyProfitTarget = 100.0;       // Daily profit target
input double DailyLossLimit = 50.0;           // Daily loss limit
input double MaxDrawdownPercent = 15.0;       // Maximum drawdown %
input bool EnableTrailingStop = true;         // Enable trailing stop
input double TrailingStopPips = 15.0;         // Trailing stop in pips
input bool EnableBreakeven = true;            // Enable breakeven
input double BreakevenPips = 10.0;            // Breakeven trigger pips

//--- Global variables
double g_dailyPnL = 0.0;
bool g_dailyTargetHit = false;
bool g_dailyLossHit = false;
int g_buyMartingaleLevel = 0;
int g_sellMartingaleLevel = 0;
double g_lotProgression[];
datetime g_lastBarTime = 0;
datetime g_lastTradeTime = 0;
int g_tradesThisHour = 0;
datetime g_currentHour = 0;

// Auto refresh variables
datetime g_lastRefreshTime = 0;
int g_tickCount = 0;

// Custom indicator handles
int g_atrHandle = INVALID_HANDLE;
int g_rsiHandle = INVALID_HANDLE;
int g_emaHandle = INVALID_HANDLE;
int g_stochHandle = INVALID_HANDLE;
int g_ampd_g65_handle = INVALID_HANDLE;
int g_ampd_75s_handle = INVALID_HANDLE;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("AMPD Jump75 Unified Optimized EA initialized");
    
    // Initialize indicators
    if(!InitializeIndicators()) {
        Print("Failed to initialize indicators");
        return INIT_FAILED;
    }
    
    // Parse lot progression
    ParseLotProgression();
    
    // Reset daily tracking
    ResetDailyTracking();

    // Initialize auto refresh
    if(EnableAutoRefresh) {
        g_lastRefreshTime = TimeCurrent();
        EventSetTimer(1); // Check every second
        Print("Auto refresh enabled - Interval: ", RefreshIntervalSeconds, " seconds");
    }

    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Release indicator handles
    if(g_atrHandle != INVALID_HANDLE) IndicatorRelease(g_atrHandle);
    if(g_rsiHandle != INVALID_HANDLE) IndicatorRelease(g_rsiHandle);
    if(g_emaHandle != INVALID_HANDLE) IndicatorRelease(g_emaHandle);
    if(g_stochHandle != INVALID_HANDLE) IndicatorRelease(g_stochHandle);
    if(g_ampd_g65_handle != INVALID_HANDLE) IndicatorRelease(g_ampd_g65_handle);
    if(g_ampd_75s_handle != INVALID_HANDLE) IndicatorRelease(g_ampd_75s_handle);

    // Kill timer
    if(EnableAutoRefresh) {
        EventKillTimer();
    }

    Print("AMPD Jump75 Unified Optimized EA deinitialized. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Auto refresh monitoring
    g_tickCount++;
    if(EnableAutoRefresh && RefreshOnNewTick && g_tickCount % 10 == 0) {
        ForceRefresh("Tick Update");
    }

    // Check if new bar on scalping timeframe
    if(!IsNewBar()) return;

    // Auto refresh on new bar
    if(EnableAutoRefresh && RefreshOnNewBar) {
        ForceRefresh("New Bar");
    }

    // Update daily P&L and check limits
    UpdateDailyPnL();
    if(CheckDailyLimits()) return;

    // Check trading filters
    if(!PassesAllFilters()) return;

    // Manage open trades
    ManageOpenTrades();

    // Check for trading signals
    CheckTradingSignals();
}

//+------------------------------------------------------------------+
//| Timer function for auto refresh                                  |
//+------------------------------------------------------------------+
void OnTimer()
{
    if(!EnableAutoRefresh) return;

    datetime current_time = TimeCurrent();

    // Check if it's time for periodic refresh
    if(current_time - g_lastRefreshTime >= RefreshIntervalSeconds) {
        ForceRefresh("Timer");
    }

    // Update status in comment
    string status = StringFormat("AMPD J75 Unified | P&L: %.2f | Buy ML: %d | Sell ML: %d | Refresh: %s",
                                 g_dailyPnL, g_buyMartingaleLevel, g_sellMartingaleLevel,
                                 TimeToString(g_lastRefreshTime, TIME_SECONDS));
    Comment(status);
}

//+------------------------------------------------------------------+
//| Initialize all indicators                                         |
//+------------------------------------------------------------------+
bool InitializeIndicators()
{
    // Standard indicators
    g_atrHandle = iATR(Symbol(), ScalpingTimeframe, 14);
    g_rsiHandle = iRSI(Symbol(), ScalpingTimeframe, RSIPeriod, PRICE_CLOSE);
    g_emaHandle = iMA(Symbol(), ScalpingTimeframe, EMAPeriod, 0, MODE_EMA, PRICE_CLOSE);
    
    if(UseStochasticFilter) {
        g_stochHandle = iStochastic(Symbol(), ScalpingTimeframe, StochKPeriod, StochDPeriod, 3, MODE_SMA, STO_LOWHIGH);
    }
    
    // Custom AMPD indicators with optimized parameters
    if(UseCustomIndicators) {
        // Load AMPD G65 V2.0 with optimized parameters for Jump75 scalping
        g_ampd_g65_handle = iCustom(Symbol(), ScalpingTimeframe, "AMPD G65 V2.0 Optimized",
                                   AMPD_G65_FastLength, AMPD_G65_SlowLength, AMPD_G65_Signal,
                                   AMPD_G65_Range, AMPD_G65_Period);

        // Load AMPD 75s M1 with optimized parameters
        g_ampd_75s_handle = iCustom(Symbol(), ScalpingTimeframe, "AMPD 75s M1 Optimized",
                                   AMPD_75s_RSI_Period, AMPD_75s_RSI_Signal, PRICE_CLOSE,
                                   AMPD_75s_Stoch_K, AMPD_75s_Stoch_D, 3, MODE_SMA, STO_LOWHIGH,
                                   AMPD_Signal_Sensitivity, 0.8, true, 21);

        // Fallback to original indicators if optimized versions not found
        if(g_ampd_g65_handle == INVALID_HANDLE) {
            Print("Optimized AMPD G65 not found, trying original...");
            g_ampd_g65_handle = iCustom(Symbol(), ScalpingTimeframe, "AMPD G65 V2.0",
                                       AMPD_G65_FastLength, AMPD_G65_SlowLength, AMPD_G65_Signal,
                                       AMPD_G65_Range, AMPD_G65_Period);
        }

        if(g_ampd_75s_handle == INVALID_HANDLE) {
            Print("Optimized AMPD 75s not found, trying original...");
            g_ampd_75s_handle = iCustom(Symbol(), ScalpingTimeframe, "AMPD 75s M1");
        }

        // Final check
        if(g_ampd_g65_handle == INVALID_HANDLE) {
            Print("Warning: AMPD G65 indicator not available, using standard indicators only");
        } else {
            Print("AMPD G65 V2.0 loaded with optimized parameters: Fast=", AMPD_G65_FastLength,
                  ", Slow=", AMPD_G65_SlowLength, ", Signal=", AMPD_G65_Signal);
        }

        if(g_ampd_75s_handle == INVALID_HANDLE) {
            Print("Warning: AMPD 75s M1 indicator not available, using standard indicators only");
        } else {
            Print("AMPD 75s M1 loaded with optimized parameters: RSI=", AMPD_75s_RSI_Period,
                  ", Sensitivity=", AMPD_Signal_Sensitivity);
        }
    }
    
    // Check if essential indicators loaded
    if(g_atrHandle == INVALID_HANDLE || g_rsiHandle == INVALID_HANDLE || g_emaHandle == INVALID_HANDLE) {
        Print("Error: Failed to initialize essential indicators");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Parse lot progression string                                     |
//+------------------------------------------------------------------+
void ParseLotProgression()
{
    string values[];
    int count = StringSplit(LotProgression, ',', values);
    ArrayResize(g_lotProgression, count);
    
    for(int i = 0; i < count; i++) {
        g_lotProgression[i] = StringToDouble(values[i]);
        if(g_lotProgression[i] <= 0) g_lotProgression[i] = LotSize;
    }
}

//+------------------------------------------------------------------+
//| Check if new bar                                                 |
//+------------------------------------------------------------------+
bool IsNewBar()
{
    datetime currentBarTime = iTime(Symbol(), ScalpingTimeframe, 0);
    if(currentBarTime != g_lastBarTime) {
        g_lastBarTime = currentBarTime;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Reset daily tracking                                             |
//+------------------------------------------------------------------+
void ResetDailyTracking()
{
    static int lastDay = -1;
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    int currentDay = dt.day;

    if(currentDay != lastDay) {
        g_dailyPnL = 0.0;
        g_dailyTargetHit = false;
        g_dailyLossHit = false;
        g_buyMartingaleLevel = 0;
        g_sellMartingaleLevel = 0;
        lastDay = currentDay;
        Print("Daily tracking reset for new day");
    }
}

//+------------------------------------------------------------------+
//| Update daily P&L                                                 |
//+------------------------------------------------------------------+
void UpdateDailyPnL()
{
    double totalProfit = 0.0;
    datetime startOfDay = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));
    
    // Calculate profit from today's closed trades
    if(HistorySelect(startOfDay, TimeCurrent())) {
        for(int i = 0; i < HistoryDealsTotal(); i++) {
            ulong ticket = HistoryDealGetTicket(i);
            if(HistoryDealGetInteger(ticket, DEAL_MAGIC) == MagicNumber) {
                totalProfit += HistoryDealGetDouble(ticket, DEAL_PROFIT);
            }
        }
    }
    
    g_dailyPnL = totalProfit;
}

//+------------------------------------------------------------------+
//| Check daily limits                                               |
//+------------------------------------------------------------------+
bool CheckDailyLimits()
{
    ResetDailyTracking();
    
    if(DailyProfitTarget > 0 && g_dailyPnL >= DailyProfitTarget && !g_dailyTargetHit) {
        g_dailyTargetHit = true;
        Print("Daily profit target reached: ", g_dailyPnL);
        Comment("Daily Profit Target Hit! P&L: ", DoubleToString(g_dailyPnL, 2));
        return true;
    }
    
    if(DailyLossLimit > 0 && g_dailyPnL <= -DailyLossLimit && !g_dailyLossHit) {
        g_dailyLossHit = true;
        Print("Daily loss limit reached: ", g_dailyPnL);
        Comment("Daily Loss Limit Hit! P&L: ", DoubleToString(g_dailyPnL, 2));
        return true;
    }
    
    return g_dailyTargetHit || g_dailyLossHit;
}

//+------------------------------------------------------------------+
//| Check all trading filters                                        |
//+------------------------------------------------------------------+
bool PassesAllFilters()
{
    // Time filter
    if(UseTimeFilter) {
        MqlDateTime dt;
        TimeToStruct(TimeCurrent(), dt);
        int currentHour = dt.hour;
        if(currentHour < StartHour || currentHour >= EndHour) {
            return false;
        }
    }
    
    // Volatility filter
    if(UseVolatilityFilter) {
        double atr[];
        if(CopyBuffer(g_atrHandle, 0, 0, 1, atr) <= 0) return false;
        if(atr[0] < MinVolatilityATR || atr[0] > MaxVolatilityATR) {
            return false;
        }
    }
    
    // Spread filter
    if(UseSpreadFilter) {
        double spread = SymbolInfoInteger(Symbol(), SYMBOL_SPREAD) * SymbolInfoDouble(Symbol(), SYMBOL_POINT);
        double pipValue = SymbolInfoDouble(Symbol(), SYMBOL_POINT) * 10;
        if(spread > MaxSpreadPips * pipValue) {
            return false;
        }
    }
    
    // Trade frequency filter
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    datetime currentHourTime = StringToTime(TimeToString(TimeCurrent(), TIME_DATE) + " " + IntegerToString(dt.hour) + ":00:00");
    if(currentHourTime != g_currentHour) {
        g_currentHour = currentHourTime;
        g_tradesThisHour = 0;
    }
    if(g_tradesThisHour >= MaxTradesPerHour) {
        return false;
    }
    
    // Minimum time between trades
    if(TimeCurrent() - g_lastTradeTime < 60) { // 1 minute minimum
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Force refresh for real-time data accuracy                        |
//+------------------------------------------------------------------+
void ForceRefresh(string reason)
{
    // Update refresh time
    g_lastRefreshTime = TimeCurrent();

    // Force chart redraw for real-time updates
    ChartRedraw();

    // Log refresh if needed (reduce logging frequency)
    static datetime last_log_time = 0;
    if(g_lastRefreshTime - last_log_time >= 60) { // Log every minute
        Print("AMPD Auto Refresh: ", reason, " at ", TimeToString(g_lastRefreshTime, TIME_SECONDS));
        last_log_time = g_lastRefreshTime;
    }
}

//+------------------------------------------------------------------+
//| Check trading signals and execute trades                         |
//+------------------------------------------------------------------+
void CheckTradingSignals()
{
    // Get current market data
    double rsi[], ema[], stoch_main[], stoch_signal[];
    double ampd_g65_lo[], ampd_g65_ro[], ampd_75s[];

    // Copy indicator buffers
    if(CopyBuffer(g_rsiHandle, 0, 0, 3, rsi) <= 0) return;
    if(CopyBuffer(g_emaHandle, 0, 0, 3, ema) <= 0) return;

    if(UseStochasticFilter && g_stochHandle != INVALID_HANDLE) {
        if(CopyBuffer(g_stochHandle, 0, 0, 3, stoch_main) <= 0) return;
        if(CopyBuffer(g_stochHandle, 1, 0, 3, stoch_signal) <= 0) return;
    }

    // Copy custom indicator buffers if available
    bool hasCustomSignals = false;
    bool hasAMPD75sSignals = false;
    double ampd_75s_signal[], ampd_75s_buy[], ampd_75s_sell[];

    if(UseCustomIndicators && g_ampd_g65_handle != INVALID_HANDLE) {
        if(CopyBuffer(g_ampd_g65_handle, 0, 0, 3, ampd_g65_lo) > 0 &&
           CopyBuffer(g_ampd_g65_handle, 1, 0, 3, ampd_g65_ro) > 0) {
            hasCustomSignals = true;
        }
    }

    // Copy AMPD 75s M1 signals
    if(UseCustomIndicators && g_ampd_75s_handle != INVALID_HANDLE) {
        if(CopyBuffer(g_ampd_75s_handle, 0, 0, 3, ampd_75s_signal) > 0 &&
           CopyBuffer(g_ampd_75s_handle, 1, 0, 3, ampd_75s_buy) > 0 &&
           CopyBuffer(g_ampd_75s_handle, 2, 0, 3, ampd_75s_sell) > 0) {
            hasAMPD75sSignals = true;
        }
    }

    // Get price data
    double close = iClose(Symbol(), ScalpingTimeframe, 0);
    double close_1 = iClose(Symbol(), ScalpingTimeframe, 1);
    double close_2 = iClose(Symbol(), ScalpingTimeframe, 2);

    // Multi-timeframe confirmation
    bool higherTFBullish = true;
    bool higherTFBearish = true;
    if(UseMultiTimeframeConfirmation) {
        double htf_ema = iMA(Symbol(), ConfirmationTimeframe, EMAPeriod, 0, MODE_EMA, PRICE_CLOSE);
        double htf_close = iClose(Symbol(), ConfirmationTimeframe, 0);
        higherTFBullish = (htf_close > htf_ema);
        higherTFBearish = (htf_close < htf_ema);
    }

    // Check for BUY signals with enhanced AMPD integration
    if(ShouldOpenBuy(rsi, ema, stoch_main, stoch_signal, ampd_g65_lo, ampd_g65_ro,
                     ampd_75s_signal, ampd_75s_buy, hasCustomSignals, hasAMPD75sSignals, higherTFBullish)) {
        if(!HasOpenPosition(POSITION_TYPE_BUY)) {
            OpenBuyTrade();
        }
    }

    // Check for SELL signals with enhanced AMPD integration
    if(ShouldOpenSell(rsi, ema, stoch_main, stoch_signal, ampd_g65_lo, ampd_g65_ro,
                      ampd_75s_signal, ampd_75s_sell, hasCustomSignals, hasAMPD75sSignals, higherTFBearish)) {
        if(!HasOpenPosition(POSITION_TYPE_SELL)) {
            OpenSellTrade();
        }
    }
}

//+------------------------------------------------------------------+
//| Optimized BUY signal logic based on loss analysis               |
//+------------------------------------------------------------------+
bool ShouldOpenBuy(const double &rsi[], const double &ema[], const double &stoch_main[],
                   const double &stoch_signal[], const double &ampd_g65_lo[], const double &ampd_g65_ro[],
                   const double &ampd_75s_signal[], const double &ampd_75s_buy[],
                   bool hasCustomSignals, bool hasAMPD75sSignals, bool higherTFBullish)
{
    double close = iClose(Symbol(), ScalpingTimeframe, 0);
    double close_1 = iClose(Symbol(), ScalpingTimeframe, 1);

    // Primary trend filter - price must be above EMA
    if(close <= ema[0]) return false;

    // Higher timeframe confirmation
    if(UseMultiTimeframeConfirmation && !higherTFBullish) return false;

    // RSI momentum filter - avoid overbought conditions
    if(rsi[0] >= RSIOverbought) return false;

    // RSI must be rising from oversold or neutral
    if(rsi[0] <= RSIOversold && rsi[0] > rsi[1]) {
        // Strong buy signal from oversold
    } else if(rsi[0] > 40 && rsi[0] < 60 && rsi[0] > rsi[1] && close > close_1) {
        // Momentum buy signal
    } else {
        return false;
    }

    // Stochastic filter
    if(UseStochasticFilter && ArraySize(stoch_main) > 0) {
        // Avoid overbought stochastic
        if(stoch_main[0] >= 80) return false;
        // Prefer stochastic rising
        if(stoch_main[0] <= stoch_signal[0]) return false;
    }

    // Enhanced AMPD custom indicator signals
    if(UseCustomIndicators) {
        // AMPD G65 V2.0 signal confirmation
        if(hasCustomSignals && ArraySize(ampd_g65_lo) > 0 && ArraySize(ampd_g65_ro) > 0) {
            // Enhanced G65 logic for Jump75 scalping
            if(ampd_g65_lo[0] != EMPTY_VALUE) {
                // Buy signal detected from G65
                Print("AMPD G65 Buy signal detected at ", ampd_g65_lo[0]);
            } else {
                // No G65 buy signal, check if we should reject
                if(ampd_g65_ro[0] != EMPTY_VALUE) return false; // Sell signal active
            }
        }

        // AMPD 75s M1 signal confirmation
        if(hasAMPD75sSignals && ArraySize(ampd_75s_buy) > 0 && ArraySize(ampd_75s_signal) > 0) {
            // Check for 75s buy zone signal
            if(ampd_75s_buy[0] == EMPTY_VALUE) {
                // No buy signal from 75s indicator
                if(ampd_75s_signal[0] > 10.0) return false; // Signal too strong against buy
            } else {
                // Strong buy signal from 75s indicator
                Print("AMPD 75s Buy signal confirmed with strength: ", ampd_75s_signal[0]);
            }
        }

        // Require at least one custom indicator confirmation if available
        if((hasCustomSignals || hasAMPD75sSignals)) {
            bool g65_confirms = !hasCustomSignals || (ArraySize(ampd_g65_lo) > 0 && ampd_g65_lo[0] != EMPTY_VALUE);
            bool ampd75s_confirms = !hasAMPD75sSignals || (ArraySize(ampd_75s_buy) > 0 && ampd_75s_buy[0] != EMPTY_VALUE);

            // At least one indicator must confirm
            if(!g65_confirms && !ampd75s_confirms) return false;
        }
    }

    // Price action confirmation - bullish candle
    if(close <= close_1) return false;

    // Additional loss prevention filters
    // Avoid trading during high volatility spikes
    double atr[];
    if(CopyBuffer(g_atrHandle, 0, 0, 2, atr) > 0) {
        if(atr[0] > atr[1] * 1.5) return false; // Avoid volatility spikes
    }

    return true;
}

//+------------------------------------------------------------------+
//| Optimized SELL signal logic based on loss analysis              |
//+------------------------------------------------------------------+
bool ShouldOpenSell(const double &rsi[], const double &ema[], const double &stoch_main[],
                    const double &stoch_signal[], const double &ampd_g65_lo[], const double &ampd_g65_ro[],
                    const double &ampd_75s_signal[], const double &ampd_75s_sell[],
                    bool hasCustomSignals, bool hasAMPD75sSignals, bool higherTFBearish)
{
    double close = iClose(Symbol(), ScalpingTimeframe, 0);
    double close_1 = iClose(Symbol(), ScalpingTimeframe, 1);

    // Primary trend filter - price must be below EMA
    if(close >= ema[0]) return false;

    // Higher timeframe confirmation
    if(UseMultiTimeframeConfirmation && !higherTFBearish) return false;

    // RSI momentum filter - avoid oversold conditions
    if(rsi[0] <= RSIOversold) return false;

    // RSI must be falling from overbought or neutral
    if(rsi[0] >= RSIOverbought && rsi[0] < rsi[1]) {
        // Strong sell signal from overbought
    } else if(rsi[0] > 40 && rsi[0] < 60 && rsi[0] < rsi[1] && close < close_1) {
        // Momentum sell signal
    } else {
        return false;
    }

    // Stochastic filter
    if(UseStochasticFilter && ArraySize(stoch_main) > 0) {
        // Avoid oversold stochastic
        if(stoch_main[0] <= 20) return false;
        // Prefer stochastic falling
        if(stoch_main[0] >= stoch_signal[0]) return false;
    }

    // Enhanced AMPD custom indicator signals
    if(UseCustomIndicators) {
        // AMPD G65 V2.0 signal confirmation
        if(hasCustomSignals && ArraySize(ampd_g65_lo) > 0 && ArraySize(ampd_g65_ro) > 0) {
            // Enhanced G65 logic for Jump75 scalping
            if(ampd_g65_ro[0] != EMPTY_VALUE) {
                // Sell signal detected from G65
                Print("AMPD G65 Sell signal detected at ", ampd_g65_ro[0]);
            } else {
                // No G65 sell signal, check if we should reject
                if(ampd_g65_lo[0] != EMPTY_VALUE) return false; // Buy signal active
            }
        }

        // AMPD 75s M1 signal confirmation
        if(hasAMPD75sSignals && ArraySize(ampd_75s_sell) > 0 && ArraySize(ampd_75s_signal) > 0) {
            // Check for 75s sell zone signal
            if(ampd_75s_sell[0] == EMPTY_VALUE) {
                // No sell signal from 75s indicator
                if(ampd_75s_signal[0] < -10.0) return false; // Signal too strong against sell
            } else {
                // Strong sell signal from 75s indicator
                Print("AMPD 75s Sell signal confirmed with strength: ", ampd_75s_signal[0]);
            }
        }

        // Require at least one custom indicator confirmation if available
        if((hasCustomSignals || hasAMPD75sSignals)) {
            bool g65_confirms = !hasCustomSignals || (ArraySize(ampd_g65_ro) > 0 && ampd_g65_ro[0] != EMPTY_VALUE);
            bool ampd75s_confirms = !hasAMPD75sSignals || (ArraySize(ampd_75s_sell) > 0 && ampd_75s_sell[0] != EMPTY_VALUE);

            // At least one indicator must confirm
            if(!g65_confirms && !ampd75s_confirms) return false;
        }
    }

    // Price action confirmation - bearish candle
    if(close >= close_1) return false;

    // Additional loss prevention filters
    // Avoid trading during high volatility spikes
    double atr[];
    if(CopyBuffer(g_atrHandle, 0, 0, 2, atr) > 0) {
        if(atr[0] > atr[1] * 1.5) return false; // Avoid volatility spikes
    }

    return true;
}

//+------------------------------------------------------------------+
//| Check if has open position of type                               |
//+------------------------------------------------------------------+
bool HasOpenPosition(ENUM_POSITION_TYPE posType)
{
    for(int i = 0; i < PositionsTotal(); i++) {
        if(positionInfo.SelectByIndex(i)) {
            if(positionInfo.Symbol() == Symbol() &&
               positionInfo.Magic() == MagicNumber &&
               positionInfo.PositionType() == posType) {
                return true;
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Calculate optimized lot size with martingale                    |
//+------------------------------------------------------------------+
double CalculateLotSize(bool isBuy)
{
    double lots = LotSize;

    if(UseMartingale) {
        int level = isBuy ? g_buyMartingaleLevel : g_sellMartingaleLevel;
        if(level < ArraySize(g_lotProgression)) {
            lots = g_lotProgression[level];
        } else {
            // Fallback to base lot if progression exceeded
            lots = LotSize;
        }
    }

    // Validate lot size
    double minLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);

    if(lots < minLot) lots = minLot;
    if(lots > maxLot) lots = maxLot;
    if(lots > MaxLotSize) lots = MaxLotSize;

    // Normalize to lot step
    lots = MathFloor(lots / lotStep) * lotStep;

    return lots;
}

//+------------------------------------------------------------------+
//| Open buy trade with optimized parameters                         |
//+------------------------------------------------------------------+
void OpenBuyTrade()
{
    double lots = CalculateLotSize(true);
    string comment = "AMPD_Buy_L" + IntegerToString(g_buyMartingaleLevel);

    trade.SetExpertMagicNumber(MagicNumber);
    trade.SetDeviationInPoints(Slippage);

    if(trade.Buy(lots, Symbol(), 0, 0, 0, comment)) {
        Print("Buy order opened: Volume=", lots, ", Comment=", comment);
        g_lastTradeTime = TimeCurrent();
        g_tradesThisHour++;
    } else {
        Print("Buy order failed: Error=", trade.ResultRetcode(), ", Comment=", trade.ResultComment());
    }
}

//+------------------------------------------------------------------+
//| Open sell trade with optimized parameters                        |
//+------------------------------------------------------------------+
void OpenSellTrade()
{
    double lots = CalculateLotSize(false);
    string comment = "AMPD_Sell_L" + IntegerToString(g_sellMartingaleLevel);

    trade.SetExpertMagicNumber(MagicNumber);
    trade.SetDeviationInPoints(Slippage);

    if(trade.Sell(lots, Symbol(), 0, 0, 0, comment)) {
        Print("Sell order opened: Volume=", lots, ", Comment=", comment);
        g_lastTradeTime = TimeCurrent();
        g_tradesThisHour++;
    } else {
        Print("Sell order failed: Error=", trade.ResultRetcode(), ", Comment=", trade.ResultComment());
    }
}

//+------------------------------------------------------------------+
//| Manage open trades with optimized risk management                |
//+------------------------------------------------------------------+
void ManageOpenTrades()
{
    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        if(!positionInfo.SelectByIndex(i)) continue;
        if(positionInfo.Symbol() != Symbol()) continue;
        if(positionInfo.Magic() != MagicNumber) continue;

        ulong ticket = positionInfo.Ticket();
        ENUM_POSITION_TYPE posType = positionInfo.PositionType();
        double openPrice = positionInfo.PriceOpen();
        double currentPrice = (posType == POSITION_TYPE_BUY) ?
                              SymbolInfoDouble(Symbol(), SYMBOL_BID) :
                              SymbolInfoDouble(Symbol(), SYMBOL_ASK);
        double sl = positionInfo.StopLoss();
        double tp = positionInfo.TakeProfit();

        double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
        double pipValue = point * 10; // Assuming 5-digit broker

        // Calculate profit in pips
        double profitPips = 0;
        if(posType == POSITION_TYPE_BUY) {
            profitPips = (currentPrice - openPrice) / pipValue;
        } else {
            profitPips = (openPrice - currentPrice) / pipValue;
        }

        // Breakeven logic
        if(EnableBreakeven && profitPips >= BreakevenPips && sl != openPrice) {
            double newSL = openPrice;
            ModifyPosition(ticket, newSL, tp);
        }

        // Trailing stop logic
        if(EnableTrailingStop && profitPips > TrailingStopPips) {
            double newSL = 0;
            bool shouldModify = false;

            if(posType == POSITION_TYPE_BUY) {
                newSL = currentPrice - (TrailingStopPips * pipValue);
                shouldModify = (sl == 0 || newSL > sl);
            } else {
                newSL = currentPrice + (TrailingStopPips * pipValue);
                shouldModify = (sl == 0 || newSL < sl);
            }

            if(shouldModify) {
                ModifyPosition(ticket, newSL, tp);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Modify position                                                  |
//+------------------------------------------------------------------+
void ModifyPosition(ulong ticket, double sl, double tp)
{
    if(trade.PositionModify(ticket, sl, tp)) {
        Print("Position ", ticket, " modified: SL=", sl, ", TP=", tp);
    } else {
        Print("Position modify failed: Error=", trade.ResultRetcode(), ", Comment=", trade.ResultComment());
    }
}

//+------------------------------------------------------------------+
//| Trade transaction event - Optimized martingale management       |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                        const MqlTradeRequest& request,
                        const MqlTradeResult& result)
{
    if(trans.symbol != Symbol()) return;

    if(trans.type == TRADE_TRANSACTION_DEAL_ADD) {
        // Check magic number for deals
        if(HistoryDealSelect(trans.deal)) {
            if(HistoryDealGetInteger(trans.deal, DEAL_MAGIC) != MagicNumber) return;
        }

        UpdateDailyPnL();

        // Update martingale levels based on deal outcome
        if(UseMartingale && HistoryDealSelect(trans.deal)) {
            double dealProfit = HistoryDealGetDouble(trans.deal, DEAL_PROFIT);
            ENUM_DEAL_ENTRY dealEntry = (ENUM_DEAL_ENTRY)HistoryDealGetInteger(trans.deal, DEAL_ENTRY);

            // Only process exit deals for martingale level updates
            if(dealEntry == DEAL_ENTRY_OUT || dealEntry == DEAL_ENTRY_INOUT) {
                ENUM_DEAL_TYPE dealType = (ENUM_DEAL_TYPE)HistoryDealGetInteger(trans.deal, DEAL_TYPE);

                if(dealProfit >= MartingaleResetProfit) {
                    // Reset martingale level on sufficient profit
                    if(dealType == DEAL_TYPE_SELL) { // Closing buy position
                        g_buyMartingaleLevel = 0;
                        Print("Buy martingale level reset to 0 (profit: ", dealProfit, ")");
                    } else if(dealType == DEAL_TYPE_BUY) { // Closing sell position
                        g_sellMartingaleLevel = 0;
                        Print("Sell martingale level reset to 0 (profit: ", dealProfit, ")");
                    }
                } else if(dealProfit < 0) {
                    // Increment martingale level on loss
                    if(dealType == DEAL_TYPE_SELL) { // Closing buy position
                        if(g_buyMartingaleLevel < MaxMartingaleLevels) {
                            g_buyMartingaleLevel++;
                            Print("Buy martingale level increased to ", g_buyMartingaleLevel, " (loss: ", dealProfit, ")");
                        } else {
                            Print("Buy martingale level at maximum (", MaxMartingaleLevels, ")");
                        }
                    } else if(dealType == DEAL_TYPE_BUY) { // Closing sell position
                        if(g_sellMartingaleLevel < MaxMartingaleLevels) {
                            g_sellMartingaleLevel++;
                            Print("Sell martingale level increased to ", g_sellMartingaleLevel, " (loss: ", dealProfit, ")");
                        } else {
                            Print("Sell martingale level at maximum (", MaxMartingaleLevels, ")");
                        }
                    }
                }
            }
        }

        // Update comment with current status
        string comment = StringFormat("AMPD J75 Unified | Daily P&L: %.2f | Buy ML: %d | Sell ML: %d",
                                     g_dailyPnL, g_buyMartingaleLevel, g_sellMartingaleLevel);
        Comment(comment);

        Print("Trade transaction processed: Daily P/L updated to: ", DoubleToString(g_dailyPnL, 2));
    }
}
